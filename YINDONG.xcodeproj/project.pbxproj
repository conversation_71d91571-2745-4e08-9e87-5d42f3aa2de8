// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		281B40442E013D6100939041 /* skill_level_100.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B40432E013D6100939041 /* skill_level_100.lottie */; };
		281B40462E01838500939041 /* PkResultPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40452E01838500939041 /* PkResultPopView.swift */; };
		281B40532E018FC400939041 /* skill_level_55.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B404A2E018FC400939041 /* skill_level_55.lottie */; };
		281B40542E018FC400939041 /* skill_level_65.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B404C2E018FC400939041 /* skill_level_65.lottie */; };
		281B40552E018FC400939041 /* skill_level_80.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B404F2E018FC400939041 /* skill_level_80.lottie */; };
		281B40562E018FC400939041 /* skill_level_85.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B40502E018FC400939041 /* skill_level_85.lottie */; };
		281B40572E018FC400939041 /* skill_level_90.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B40512E018FC400939041 /* skill_level_90.lottie */; };
		281B40582E018FC400939041 /* skill_level_70.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B404D2E018FC400939041 /* skill_level_70.lottie */; };
		281B40592E018FC400939041 /* skill_level_50.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B40492E018FC400939041 /* skill_level_50.lottie */; };
		281B405A2E018FC400939041 /* skill_level_75.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B404E2E018FC400939041 /* skill_level_75.lottie */; };
		281B405B2E018FC400939041 /* skill_level_60.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B404B2E018FC400939041 /* skill_level_60.lottie */; };
		281B405C2E018FC400939041 /* skill_level_45.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B40482E018FC400939041 /* skill_level_45.lottie */; };
		281B405D2E018FC400939041 /* skill_level_95.lottie in Resources */ = {isa = PBXBuildFile; fileRef = 281B40522E018FC400939041 /* skill_level_95.lottie */; };
		281B40612E02AC8C00939041 /* EnvConfigVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40602E02AC8C00939041 /* EnvConfigVC.swift */; };
		281B40632E02ACFB00939041 /* CustomConfigVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40622E02ACFB00939041 /* CustomConfigVC.swift */; };
		281B40662E02D39600939041 /* MansionMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40652E02D39600939041 /* MansionMainVC.swift */; };
		281B40682E02DA1400939041 /* work_new_select.svga in Resources */ = {isa = PBXBuildFile; fileRef = 281B40672E02DA1400939041 /* work_new_select.svga */; };
		281B406C2E02E1FC00939041 /* MansionMainModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B406B2E02E1FC00939041 /* MansionMainModel.swift */; };
		281B40722E02E94B00939041 /* MansionAttendantSectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B406E2E02E94B00939041 /* MansionAttendantSectionView.swift */; };
		281B40732E02E94B00939041 /* MansionHeaderInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B406F2E02E94B00939041 /* MansionHeaderInfoView.swift */; };
		281B40742E02E94B00939041 /* MansionMasterSectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40702E02E94B00939041 /* MansionMasterSectionView.swift */; };
		281B40752E02E94B00939041 /* MansionWorkSectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40712E02E94B00939041 /* MansionWorkSectionView.swift */; };
		281B40762E02E94B00939041 /* MansionAttendantCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B406D2E02E94B00939041 /* MansionAttendantCell.swift */; };
		281B40782E03E9DC00939041 /* MansionTaskSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40772E03E9DC00939041 /* MansionTaskSheetView.swift */; };
		281B407A2E042FC500939041 /* MansionMangerListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40792E042FC500939041 /* MansionMangerListVC.swift */; };
		281B407C2E043C3800939041 /* MansionSytemPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B407B2E043C3800939041 /* MansionSytemPopView.swift */; };
		281B407E2E050EB800939041 /* MansionHisPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B407D2E050EB800939041 /* MansionHisPopView.swift */; };
		281B40812E05583900939041 /* RuneDisplayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40802E05583900939041 /* RuneDisplayView.swift */; };
		281B40832E08E10B00939041 /* RuneDisplayInfoListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40822E08E10B00939041 /* RuneDisplayInfoListView.swift */; };
		281B40852E09614D00939041 /* TuLongRewardWithNameCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40842E09614D00939041 /* TuLongRewardWithNameCell.swift */; };
		281B40872E0A40B500939041 /* MineUserVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40862E0A40B500939041 /* MineUserVC.swift */; };
		281B408B2E0A436100939041 /* MenuItemManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40882E0A436100939041 /* MenuItemManager.swift */; };
		281B408C2E0A436100939041 /* MenuItemModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B40892E0A436100939041 /* MenuItemModel.swift */; };
		281B40902E0A43CC00939041 /* UserHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B408F2E0A43CC00939041 /* UserHeaderView.swift */; };
		281B40912E0A43CC00939041 /* ServiceMenuView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B408E2E0A43CC00939041 /* ServiceMenuView.swift */; };
		281B40922E0A43CC00939041 /* FunctionGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281B408D2E0A43CC00939041 /* FunctionGridView.swift */; };
		281C6FC92DFFFB3D002F9D99 /* UseSkillCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281C6FC82DFFFB3D002F9D99 /* UseSkillCell.swift */; };
		281C6FD52DFFFF43002F9D99 /* skill_talk_success.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FD32DFFFF43002F9D99 /* skill_talk_success.json */; };
		281C6FD62DFFFF43002F9D99 /* skill_hole_fail.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FCE2DFFFF43002F9D99 /* skill_hole_fail.json */; };
		281C6FD72DFFFF43002F9D99 /* skill_catch_fail.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FCA2DFFFF43002F9D99 /* skill_catch_fail.json */; };
		281C6FD82DFFFF43002F9D99 /* skill_kiss_fail.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FD02DFFFF43002F9D99 /* skill_kiss_fail.json */; };
		281C6FD92DFFFF43002F9D99 /* skill_kiss_success.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FD12DFFFF43002F9D99 /* skill_kiss_success.json */; };
		281C6FDA2DFFFF43002F9D99 /* skill_hole_success.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FCF2DFFFF43002F9D99 /* skill_hole_success.json */; };
		281C6FDB2DFFFF43002F9D99 /* skill_foot_fail.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FCC2DFFFF43002F9D99 /* skill_foot_fail.json */; };
		281C6FDC2DFFFF43002F9D99 /* skill_foot_success.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FCD2DFFFF43002F9D99 /* skill_foot_success.json */; };
		281C6FDD2DFFFF43002F9D99 /* skill_talk_fail.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FD22DFFFF43002F9D99 /* skill_talk_fail.json */; };
		281C6FDE2DFFFF43002F9D99 /* skill_catch_success.json in Resources */ = {isa = PBXBuildFile; fileRef = 281C6FCB2DFFFF43002F9D99 /* skill_catch_success.json */; };
		281C6FE12E000389002F9D99 /* GameUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 281C6FE02E000389002F9D99 /* GameUtils.swift */; };
		282188922E0E2F7400362550 /* ScreenManagement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282188912E0E2F7400362550 /* ScreenManagement.swift */; };
		2821891F2E0E8E6200362550 /* ZuoJiaMangement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2821891E2E0E8E6200362550 /* ZuoJiaMangement.swift */; };
		2825A3B62DF6788F00EE425C /* RedPacketRainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3B52DF6788F00EE425C /* RedPacketRainView.swift */; };
		2825A3B82DF6B66C00EE425C /* TuLongMainSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3B72DF6B66C00EE425C /* TuLongMainSheetView.swift */; };
		2825A3BB2DF6B75B00EE425C /* TaskCarousel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3BA2DF6B75B00EE425C /* TaskCarousel.swift */; };
		2825A3BE2DF6BF1400EE425C /* RoomTaskModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3BD2DF6BF1400EE425C /* RoomTaskModel.swift */; };
		2825A3C02DF6BFD100EE425C /* TLCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3BF2DF6BFD100EE425C /* TLCell.swift */; };
		2825A3C32DF6D14700EE425C /* dragon_enter.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 2825A3C12DF6D14700EE425C /* dragon_enter.mp4 */; };
		2825A3C42DF6D14700EE425C /* dragon_normal.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 2825A3C22DF6D14700EE425C /* dragon_normal.mp4 */; };
		2825A3C62DF7C8A200EE425C /* SheetWkWebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3C52DF7C8A200EE425C /* SheetWkWebView.swift */; };
		2825A3C82DF7E0DB00EE425C /* DragonHurt.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2825A3C72DF7E0DB00EE425C /* DragonHurt.ttf */; };
		2825A3CA2DF835C800EE425C /* TuLongRedLuckPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3C92DF835C700EE425C /* TuLongRedLuckPopView.swift */; };
		2825A3CC2DF8397000EE425C /* CenteredCollectionViewFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3CB2DF8397000EE425C /* CenteredCollectionViewFlowLayout.swift */; };
		2825A3CE2DF8409100EE425C /* AlibabaPuHuiTi_2_115_Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2825A3CD2DF8409100EE425C /* AlibabaPuHuiTi_2_115_Black.ttf */; };
		2825A3D02DF852D500EE425C /* TuLongRecordSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3CF2DF852D500EE425C /* TuLongRecordSheetView.swift */; };
		2825A3D32DF8571A00EE425C /* TuLongRecordModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3D12DF8571A00EE425C /* TuLongRecordModel.swift */; };
		2825A3D52DF8572500EE425C /* TuLongRecordCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3D42DF8572500EE425C /* TuLongRecordCell.swift */; };
		2825A3D72DF8572D00EE425C /* TuLongRecordEmptyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3D62DF8572D00EE425C /* TuLongRecordEmptyView.swift */; };
		2825A3D92DF8617F00EE425C /* TuLongAttMainSheetV.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3D82DF8617F00EE425C /* TuLongAttMainSheetV.swift */; };
		2825A3DB2DF862E500EE425C /* TuLongBaseAttRankListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3DA2DF862E500EE425C /* TuLongBaseAttRankListVC.swift */; };
		2825A3DD2DF91B2D00EE425C /* TuLongRankModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3DC2DF91B2D00EE425C /* TuLongRankModel.swift */; };
		2825A3E12DF91B7E00EE425C /* TuLongRewardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3E02DF91B7E00EE425C /* TuLongRewardCell.swift */; };
		2825A3E22DF91B7E00EE425C /* TuLongRankCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3DF2DF91B7E00EE425C /* TuLongRankCell.swift */; };
		2825A3E32DF91B7E00EE425C /* TuLongMyRankBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3DE2DF91B7E00EE425C /* TuLongMyRankBarView.swift */; };
		2825A3E52DF9281800EE425C /* TuLongRoomRankListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3E42DF9281800EE425C /* TuLongRoomRankListVC.swift */; };
		2825A3E72DF92DC700EE425C /* TuLongRoomRankItemVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3E62DF92DC700EE425C /* TuLongRoomRankItemVC.swift */; };
		2825A3EB2DF9527F00EE425C /* UniversalTickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3E82DF9527F00EE425C /* UniversalTickerView.swift */; };
		2825A3EE2DF952BC00EE425C /* TuLongTickerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3EC2DF952BC00EE425C /* TuLongTickerManager.swift */; };
		2825A3F02DF95AD500EE425C /* TuLongAttCarouseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3EF2DF95AD500EE425C /* TuLongAttCarouseView.swift */; };
		2825A3F22DF96DEC00EE425C /* TulongSettlementPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3F12DF96DEC00EE425C /* TulongSettlementPopView.swift */; };
		2825A3F42DF9721000EE425C /* TuLongSettlementCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3F32DF9721000EE425C /* TuLongSettlementCell.swift */; };
		2825A3F62DF99A1D00EE425C /* TaskCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3F52DF99A1D00EE425C /* TaskCell.swift */; };
		2825A3F82DFA650400EE425C /* TaskSurePopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3F72DFA650400EE425C /* TaskSurePopView.swift */; };
		2825A3FA2DFA844700EE425C /* ExitRoomTaskPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3F92DFA844700EE425C /* ExitRoomTaskPopView.swift */; };
		2825A3FC2DFAAB6E00EE425C /* AdvertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3FB2DFAAB6E00EE425C /* AdvertView.swift */; };
		2825A3FE2DFAB40900EE425C /* ComboButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A3FD2DFAB40900EE425C /* ComboButton.swift */; };
		2825A4012DFABE3900EE425C /* RedBagBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2825A4002DFABE3900EE425C /* RedBagBaseModel.swift */; };
		282804DB2D880C540015FA35 /* EditTagSeletedSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282804DA2D880C540015FA35 /* EditTagSeletedSheetView.swift */; };
		282804DD2D8817140015FA35 /* TagBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282804DC2D8817140015FA35 /* TagBaseModel.swift */; };
		282804E02D8832150015FA35 /* TagBaseMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282804DF2D8832150015FA35 /* TagBaseMainVC.swift */; };
		282804E22D8832440015FA35 /* TagOptionListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282804E12D8832440015FA35 /* TagOptionListVC.swift */; };
		282805472D8984620015FA35 /* EditAdressSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282805462D8984620015FA35 /* EditAdressSheetView.swift */; };
		2828054C2D8994C70015FA35 /* UserPageMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2828054B2D8994C70015FA35 /* UserPageMainVC.swift */; };
		2828054E2D8995220015FA35 /* UserPageHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2828054D2D8995220015FA35 /* UserPageHeaderView.swift */; };
		282805502D8998920015FA35 /* UserCardListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2828054F2D8998920015FA35 /* UserCardListVC.swift */; };
		282805962D8A63EF0015FA35 /* BasePageInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282805952D8A63EF0015FA35 /* BasePageInfoView.swift */; };
		282805AE2D8A99130015FA35 /* UserCardInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282805AD2D8A99130015FA35 /* UserCardInfoView.swift */; };
		282805B02D8A9B220015FA35 /* UserCardPhotoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282805AF2D8A9B220015FA35 /* UserCardPhotoView.swift */; };
		282805B22D8AA1460015FA35 /* UserTagCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282805B12D8AA1460015FA35 /* UserTagCardView.swift */; };
		282805B42D8AD4BC0015FA35 /* UserPageBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282805B32D8AD4BC0015FA35 /* UserPageBottomView.swift */; };
		2828064C2D8C12430015FA35 /* PearlsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2828064B2D8C12430015FA35 /* PearlsModel.swift */; };
		2828064F2D8C12570015FA35 /* PearlsListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2828064E2D8C12570015FA35 /* PearlsListVC.swift */; };
		282806522D90F42F0015FA35 /* PearlsListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806512D90F42F0015FA35 /* PearlsListCell.swift */; };
		282806552D91319F0015FA35 /* RechargeCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806542D91319F0015FA35 /* RechargeCardView.swift */; };
		282806572D9136D60015FA35 /* UserRechargeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806562D9136D60015FA35 /* UserRechargeModel.swift */; };
		282806592D9138910015FA35 /* UserLevelInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806582D9138910015FA35 /* UserLevelInfoModel.swift */; };
		2828066C2D926B630015FA35 /* RecordingPanelController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2828066B2D926B630015FA35 /* RecordingPanelController.swift */; };
		282806AE2D92A1670015FA35 /* LwChatListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806AD2D92A1670015FA35 /* LwChatListVC.swift */; };
		282806B12D92A3A10015FA35 /* LwChatUserCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806B02D92A3A10015FA35 /* LwChatUserCell.swift */; };
		282806B42D92A3EE0015FA35 /* LwSessionModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806B32D92A3EE0015FA35 /* LwSessionModel.swift */; };
		282806B72D92B1670015FA35 /* LwChatDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806B62D92B1670015FA35 /* LwChatDetailVC.swift */; };
		282806B92D92B29C0015FA35 /* MessageHandlerProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806B82D92B29C0015FA35 /* MessageHandlerProtocol.swift */; };
		282806BC2D92B8C00015FA35 /* MsgBaseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806BB2D92B8C00015FA35 /* MsgBaseCell.swift */; };
		282806BE2D92B8D30015FA35 /* TextMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806BD2D92B8D30015FA35 /* TextMsgCell.swift */; };
		282806C02D92B8F60015FA35 /* ImageMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806BF2D92B8F60015FA35 /* ImageMsgCell.swift */; };
		282806C22D92B92B0015FA35 /* NoticeMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806C12D92B92B0015FA35 /* NoticeMsgCell.swift */; };
		282806C52D92B9D80015FA35 /* MsgBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806C42D92B9D80015FA35 /* MsgBaseModel.swift */; };
		282806C72D92D4F10015FA35 /* GiftMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806C62D92D4F10015FA35 /* GiftMsgCell.swift */; };
		282806CA2D92D77F0015FA35 /* GiftModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806C92D92D77F0015FA35 /* GiftModel.swift */; };
		282806CE2D93D1580015FA35 /* ChatCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282806CD2D93D1580015FA35 /* ChatCardCell.swift */; };
		282F07012D9E873D000EDE0F /* UserPhoneAuthVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F07002D9E873D000EDE0F /* UserPhoneAuthVC.swift */; };
		282F07042DA36F7B000EDE0F /* ReportViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F07022DA36F7B000EDE0F /* ReportViewController.swift */; };
		282F07062DA3B86B000EDE0F /* InteractListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F07052DA3B86B000EDE0F /* InteractListVC.swift */; };
		282F07082DA4B45B000EDE0F /* UserDynamicCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F07072DA4B45B000EDE0F /* UserDynamicCell.swift */; };
		282F070A2DA4B587000EDE0F /* UserListDynamicVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F07092DA4B587000EDE0F /* UserListDynamicVC.swift */; };
		282F070C2DA60C05000EDE0F /* ChatWarningView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F070B2DA60C05000EDE0F /* ChatWarningView.swift */; };
		282F070E2DA61022000EDE0F /* FriendRemarkVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F070D2DA61022000EDE0F /* FriendRemarkVC.swift */; };
		282F07102DA6206A000EDE0F /* IntimacyTipPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F070F2DA6206A000EDE0F /* IntimacyTipPopView.swift */; };
		282F07132DA67767000EDE0F /* ChatHelp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 282F07122DA67767000EDE0F /* ChatHelp.swift */; };
		283260032D9A3A0B007465A1 /* LwChatMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260022D9A3A0B007465A1 /* LwChatMainVC.swift */; };
		283260072D9A6EC3007465A1 /* LwFriendsMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260062D9A6EC3007465A1 /* LwFriendsMainVC.swift */; };
		2832600A2D9A6ED3007465A1 /* LwFriendsListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260092D9A6ED3007465A1 /* LwFriendsListVC.swift */; };
		2832600C2D9A6EE8007465A1 /* FriendBaseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832600B2D9A6EE8007465A1 /* FriendBaseCell.swift */; };
		2832600F2D9A79E8007465A1 /* FriendModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832600E2D9A79E8007465A1 /* FriendModel.swift */; };
		283260122D9A7E8E007465A1 /* numFont.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 283260112D9A7E8E007465A1 /* numFont.ttf */; };
		283260142D9A97C4007465A1 /* HobbyListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260132D9A97C4007465A1 /* HobbyListVC.swift */; };
		283260162D9A9932007465A1 /* HobbyUserCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260152D9A9932007465A1 /* HobbyUserCell.swift */; };
		283260182D9A9A48007465A1 /* HobbyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260172D9A9A48007465A1 /* HobbyModel.swift */; };
		2832601A2D9A9FDC007465A1 /* UserGiftListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283260192D9A9FDC007465A1 /* UserGiftListVC.swift */; };
		2832601C2D9AA206007465A1 /* NotificationPromptView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832601B2D9AA206007465A1 /* NotificationPromptView.swift */; };
		2832601E2D9AA3BB007465A1 /* SyteamCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832601D2D9AA3BB007465A1 /* SyteamCell.swift */; };
		283260202D9AA3EE007465A1 /* SyteamModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832601F2D9AA3EE007465A1 /* SyteamModel.swift */; };
		283263DC2D9B8425007465A1 /* UserFeatureConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263DB2D9B8425007465A1 /* UserFeatureConfig.swift */; };
		283263DE2D9B85AA007465A1 /* UserGiftListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263DD2D9B85AA007465A1 /* UserGiftListCell.swift */; };
		283263E02D9B98B5007465A1 /* ChatStatusPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263DF2D9B98B5007465A1 /* ChatStatusPickerView.swift */; };
		283263E22D9BBF37007465A1 /* PopoverMenuViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263E12D9BBF37007465A1 /* PopoverMenuViewController.swift */; };
		283263E42D9BC3C9007465A1 /* AddFreiendListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263E32D9BC3C9007465A1 /* AddFreiendListVC.swift */; };
		283263E62D9BE285007465A1 /* LwBaseWebVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263E52D9BE285007465A1 /* LwBaseWebVC.swift */; };
		283263E82D9BE704007465A1 /* WebURL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263E72D9BE704007465A1 /* WebURL.swift */; };
		283263EB2D9BFBB9007465A1 /* AccountPhoneVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263EA2D9BFBB9007465A1 /* AccountPhoneVC.swift */; };
		283263ED2D9C02B9007465A1 /* VerifyOldPhoneVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263EC2D9C02B9007465A1 /* VerifyOldPhoneVC.swift */; };
		283263EF2D9C08FB007465A1 /* VerifyNewPhoneVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263EE2D9C08FB007465A1 /* VerifyNewPhoneVC.swift */; };
		283263F22D9C0F98007465A1 /* RealNameAuthVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263F12D9C0F98007465A1 /* RealNameAuthVC.swift */; };
		283263F42D9C0FD5007465A1 /* RealNameAuthSuccessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263F32D9C0FD5007465A1 /* RealNameAuthSuccessView.swift */; };
		283263F62D9C1A0F007465A1 /* PrivacySecurityVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263F52D9C1A0F007465A1 /* PrivacySecurityVC.swift */; };
		283263F82D9C1A51007465A1 /* SystemPermissionVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263F72D9C1A51007465A1 /* SystemPermissionVC.swift */; };
		283263FB2D9C1BB5007465A1 /* PermissionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263FA2D9C1BB5007465A1 /* PermissionManager.swift */; };
		283263FD2D9C1DE0007465A1 /* PermissionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263FC2D9C1DE0007465A1 /* PermissionCell.swift */; };
		283263FF2D9CC544007465A1 /* NotificationSettingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283263FE2D9CC544007465A1 /* NotificationSettingVC.swift */; };
		283264012D9CE0FA007465A1 /* DeleteAccountAgreementVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264002D9CE0FA007465A1 /* DeleteAccountAgreementVC.swift */; };
		283264042D9CEAFC007465A1 /* DeleteAccountCheckModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264022D9CEAFC007465A1 /* DeleteAccountCheckModel.swift */; };
		283264062D9CEB21007465A1 /* DeleteAccountCheckVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264052D9CEB21007465A1 /* DeleteAccountCheckVC.swift */; };
		283264082D9CED23007465A1 /* DeleteAccountConfirmVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264072D9CED23007465A1 /* DeleteAccountConfirmVC.swift */; };
		2832640C2D9D1771007465A1 /* AboutUsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832640B2D9D1771007465A1 /* AboutUsVC.swift */; };
		2832640F2D9D50F7007465A1 /* VoiceCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832640E2D9D50F7007465A1 /* VoiceCell.swift */; };
		283264602D9E22AA007465A1 /* GiftCollectionVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832645F2D9E22AA007465A1 /* GiftCollectionVC.swift */; };
		283264632D9E22CE007465A1 /* GiftBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264622D9E22CE007465A1 /* GiftBaseModel.swift */; };
		283264662D9E2459007465A1 /* GiftItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264652D9E2459007465A1 /* GiftItemCell.swift */; };
		283264682D9E25A5007465A1 /* GiftPanelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264672D9E25A5007465A1 /* GiftPanelView.swift */; };
		2832646A2D9E26D1007465A1 /* GiftPanelFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283264692D9E26D1007465A1 /* GiftPanelFooterView.swift */; };
		2832646C2D9E2848007465A1 /* GiftQuantitySelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2832646B2D9E2848007465A1 /* GiftQuantitySelectionView.swift */; };
		************************ /* PlayerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1902DACE8B50086DDB2 /* PlayerManager.swift */; };
		283AB1932DACEEBF0086DDB2 /* AnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1922DACEEBF0086DDB2 /* AnimationPlayer.swift */; };
		283AB1952DACEF260086DDB2 /* PNGAnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1942DACEF260086DDB2 /* PNGAnimationPlayer.swift */; };
		283AB1972DACF2700086DDB2 /* SVGAAnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1962DACF2700086DDB2 /* SVGAAnimationPlayer.swift */; };
		283AB1992DACFCDE0086DDB2 /* LottieAnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1982DACFCDE0086DDB2 /* LottieAnimationPlayer.swift */; };
		283AB19C2DACFF680086DDB2 /* LSZipLottieFileManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB19B2DACFF680086DDB2 /* LSZipLottieFileManager.swift */; };
		283AB19E2DAD031C0086DDB2 /* VapAnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB19D2DAD031C0086DDB2 /* VapAnimationPlayer.swift */; };
		283AB1A02DAD048F0086DDB2 /* MP4AnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB19F2DAD048F0086DDB2 /* MP4AnimationPlayer.swift */; };
		283AB1A22DAD09500086DDB2 /* FileManagerWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1A12DAD09500086DDB2 /* FileManagerWrapper.swift */; };
		283AB1A42DAD0B190086DDB2 /* AnimationPlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB1A32DAD0B190086DDB2 /* AnimationPlayView.swift */; };
		283AB34A2DB0D1290086DDB2 /* HomeRoomListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB3492DB0D1290086DDB2 /* HomeRoomListVC.swift */; };
		283AB34C2DB0D1890086DDB2 /* HomeRoomBaseVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB34B2DB0D1890086DDB2 /* HomeRoomBaseVC.swift */; };
		283AB34F2DB0D5380086DDB2 /* RoomManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB34E2DB0D5380086DDB2 /* RoomManger.swift */; };
		283AB3522DB0D9360086DDB2 /* RoomInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB3512DB0D9360086DDB2 /* RoomInfoModel.swift */; };
		283AB3562DB0DAAA0086DDB2 /* RoomSmallCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB3542DB0DAAA0086DDB2 /* RoomSmallCell.swift */; };
		283AB3572DB0DAAA0086DDB2 /* RoomBigCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 283AB3532DB0DAAA0086DDB2 /* RoomBigCell.swift */; };
		283D57062DF28DA6001C4D67 /* login_new.zip in Resources */ = {isa = PBXBuildFile; fileRef = 283D57052DF28DA6001C4D67 /* login_new.zip */; };
		2840B2862D828629007C907C /* GenericActionSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840B2852D828629007C907C /* GenericActionSheetView.swift */; };
		2840EDA22D82E68D000A7EA5 /* DynamicNoticeVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDA12D82E68D000A7EA5 /* DynamicNoticeVC.swift */; };
		2840EDA42D82F248000A7EA5 /* DynoticeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDA32D82F248000A7EA5 /* DynoticeModel.swift */; };
		2840EDA62D82F32A000A7EA5 /* DyNoticeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDA52D82F32A000A7EA5 /* DyNoticeCell.swift */; };
		2840EDEC2D83C8FC000A7EA5 /* EditMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDEB2D83C8FC000A7EA5 /* EditMainVC.swift */; };
		2840EDF22D83CF10000A7EA5 /* UserInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDF12D83CF10000A7EA5 /* UserInfoModel.swift */; };
		2840EDF42D840A6A000A7EA5 /* EditProfileModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDF32D840A6A000A7EA5 /* EditProfileModel.swift */; };
		2840EDF82D840A77000A7EA5 /* BaseEditProfileCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDF62D840A77000A7EA5 /* BaseEditProfileCell.swift */; };
		2840EDFA2D840B54000A7EA5 /* EditPhotoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDF92D840B54000A7EA5 /* EditPhotoCell.swift */; };
		2840EDFC2D840B7C000A7EA5 /* EditTextInfoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDFB2D840B7C000A7EA5 /* EditTextInfoCell.swift */; };
		2840EDFE2D840BAA000A7EA5 /* EditUserAlbumCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDFD2D840BAA000A7EA5 /* EditUserAlbumCell.swift */; };
		2840EE002D840BC6000A7EA5 /* EditTagListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EDFF2D840BC6000A7EA5 /* EditTagListCell.swift */; };
		2840EE022D841CDD000A7EA5 /* EditProfileDataProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EE012D841CDD000A7EA5 /* EditProfileDataProvider.swift */; };
		2840EE042D8431BD000A7EA5 /* EditUserTextSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2840EE032D8431BD000A7EA5 /* EditUserTextSheetView.swift */; };
		2849F88F2DD31AD300FA9D53 /* FloatingScreenView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F88E2DD31AD300FA9D53 /* FloatingScreenView.swift */; };
		2849F8912DD31CD000FA9D53 /* BossChestStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8902DD31CD000FA9D53 /* BossChestStatusView.swift */; };
		2849F8952DD3539600FA9D53 /* BossAttackManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8942DD3539600FA9D53 /* BossAttackManager.swift */; };
		2849F8982DD3718700FA9D53 /* SkillMainListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8972DD3718700FA9D53 /* SkillMainListVC.swift */; };
		2849F89A2DD3719700FA9D53 /* SkillListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8992DD3719700FA9D53 /* SkillListVC.swift */; };
		2849F89D2DD4392400FA9D53 /* LipPowerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F89C2DD4392400FA9D53 /* LipPowerView.swift */; };
		2849F89F2DD43A9000FA9D53 /* UpgradeCostView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F89E2DD43A9000FA9D53 /* UpgradeCostView.swift */; };
		2849F8A12DD43EAE00FA9D53 /* PointsExchangeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8A02DD43EAE00FA9D53 /* PointsExchangeView.swift */; };
		2849F8A42DD46E9D00FA9D53 /* SkillItemModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8A32DD46E9D00FA9D53 /* SkillItemModel.swift */; };
		2849F8A62DD48F3900FA9D53 /* RoomHtmlMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8A52DD48F3900FA9D53 /* RoomHtmlMsgCell.swift */; };
		2849F8A82DD597CB00FA9D53 /* SkillLevelRatingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8A72DD597CB00FA9D53 /* SkillLevelRatingView.swift */; };
		2849F8AA2DD5BD1A00FA9D53 /* LottieAnimationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8A92DD5BD1A00FA9D53 /* LottieAnimationManager.swift */; };
		2849F8B02DD5E0EC00FA9D53 /* LuZiPaySheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8AF2DD5E0EC00FA9D53 /* LuZiPaySheetView.swift */; };
		2849F8B22DD5E20300FA9D53 /* UserLevelMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8B12DD5E20300FA9D53 /* UserLevelMainVC.swift */; };
		2849F8B42DD5F1E700FA9D53 /* UserYuanbaoVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2849F8B32DD5F1E700FA9D53 /* UserYuanbaoVC.swift */; };
		28553F502D87C3710042C619 /* CommonAlignFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28553F4F2D87C3710042C619 /* CommonAlignFlowLayout.swift */; };
		285DA6482DFAC1EB00BDCDAD /* RedActView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285DA6472DFAC1EB00BDCDAD /* RedActView.swift */; };
		285DA64A2DFACFF500BDCDAD /* BossRedRobView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285DA6492DFACFF500BDCDAD /* BossRedRobView.swift */; };
		285DA64C2DFAF02900BDCDAD /* BossRedRedBegainV.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285DA64B2DFAF02900BDCDAD /* BossRedRedBegainV.swift */; };
		285DA64E2DFAFD9600BDCDAD /* RedLuckUserPopV.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285DA64D2DFAFD9600BDCDAD /* RedLuckUserPopV.swift */; };
		285DA6512DFBFF2900BDCDAD /* UserPkSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285DA6502DFBFF2900BDCDAD /* UserPkSheetView.swift */; };
		285DA6542DFC1D1A00BDCDAD /* SkillInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285DA6532DFC1D1A00BDCDAD /* SkillInfoModel.swift */; };
		2866EB802DC1D743005B0D13 /* RoomUserMainSheetV.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2866EB7F2DC1D743005B0D13 /* RoomUserMainSheetV.swift */; };
		2866EB822DC1D75B005B0D13 /* OnlineListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2866EB812DC1D75B005B0D13 /* OnlineListVC.swift */; };
		2866EB842DC1D766005B0D13 /* RankListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2866EB832DC1D766005B0D13 /* RankListVC.swift */; };
		28785FE62E0A7D9900367787 /* UserInfoCollectionModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FE52E0A7D9900367787 /* UserInfoCollectionModel.swift */; };
		28785FE92E0A8B4B00367787 /* MainGoodsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FE82E0A8B4B00367787 /* MainGoodsVC.swift */; };
		28785FEC2E0A91FB00367787 /* DressUpListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FEB2E0A91FB00367787 /* DressUpListCell.swift */; };
		28785FEF2E0A970800367787 /* DressBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FEE2E0A970800367787 /* DressBaseModel.swift */; };
		28785FF12E0A9A1600367787 /* DecorationMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FF02E0A9A1600367787 /* DecorationMainVC.swift */; };
		28785FF32E0A9A2800367787 /* DecorationListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FF22E0A9A2800367787 /* DecorationListVC.swift */; };
		28785FF52E0AAFA200367787 /* RingListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FF42E0AAFA200367787 /* RingListVC.swift */; };
		28785FF72E0ABC5C00367787 /* HotGoodsListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FF62E0ABC5C00367787 /* HotGoodsListVC.swift */; };
		28785FF92E0AC85F00367787 /* DressBuyPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FF82E0AC85F00367787 /* DressBuyPopView.swift */; };
		28785FFB2E0B887200367787 /* ShowSpecialView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FFA2E0B887200367787 /* ShowSpecialView.swift */; };
		28785FFD2E0BA49400367787 /* BaseChatBgView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FFC2E0BA49400367787 /* BaseChatBgView.swift */; };
		28785FFF2E0BDB5300367787 /* EventPreviewShowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28785FFE2E0BDB5300367787 /* EventPreviewShowView.swift */; };
		287860012E0BEBA200367787 /* PurchaseSuccessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287860002E0BEBA200367787 /* PurchaseSuccessView.swift */; };
		287860042E0BF63700367787 /* MyPackMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287860032E0BF63700367787 /* MyPackMainVC.swift */; };
		287860062E0BF64F00367787 /* MyPackListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287860052E0BF64F00367787 /* MyPackListVC.swift */; };
		287860082E0C0F8E00367787 /* WearPrePopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287860072E0C0F8E00367787 /* WearPrePopView.swift */; };
		2878600A2E0C1D7B00367787 /* RingBuyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287860092E0C1D7B00367787 /* RingBuyView.swift */; };
		2878600C2E0CF46F00367787 /* APIHistorySheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2878600B2E0CF46F00367787 /* APIHistorySheetView.swift */; };
		287E93542DDEB655004EEDFB /* RoomNoticeEditVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93532DDEB655004EEDFB /* RoomNoticeEditVC.swift */; };
		287E93562DDEF71D004EEDFB /* RoomAdminListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93552DDEF71D004EEDFB /* RoomAdminListVC.swift */; };
		287E93582DDEF728004EEDFB /* RoomAdminAddVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93572DDEF728004EEDFB /* RoomAdminAddVC.swift */; };
		287E93F92DE06F1A004EEDFB /* GiftQueManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93F82DE06F1A004EEDFB /* GiftQueManager.swift */; };
		287E93FB2DE06F4A004EEDFB /* QueGift.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93FA2DE06F4A004EEDFB /* QueGift.swift */; };
		287E93FD2DE06F78004EEDFB /* GiftShowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93FC2DE06F78004EEDFB /* GiftShowView.swift */; };
		287E93FF2DE06FA8004EEDFB /* GiftNumView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E93FE2DE06FA8004EEDFB /* GiftNumView.swift */; };
		287E94012DE3FF0D004EEDFB /* RoomBgListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94002DE3FF0D004EEDFB /* RoomBgListVC.swift */; };
		287E94032DE43C9E004EEDFB /* RoomGiftOpenSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94022DE43C9E004EEDFB /* RoomGiftOpenSheetView.swift */; };
		287E94062DE45DA2004EEDFB /* RoomSongMainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94052DE45DA2004EEDFB /* RoomSongMainView.swift */; };
		287E94082DE463B8004EEDFB /* RoomSongListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94072DE463B8004EEDFB /* RoomSongListVC.swift */; };
		287E940B2DE46823004EEDFB /* MusicPlaybackManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E940A2DE46823004EEDFB /* MusicPlaybackManager.swift */; };
		287E940D2DE4722D004EEDFB /* MusicPlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E940C2DE4722D004EEDFB /* MusicPlayView.swift */; };
		287E940F2DE47358004EEDFB /* RoomSongCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E940E2DE47358004EEDFB /* RoomSongCell.swift */; };
		287E94112DE54DE2004EEDFB /* RoomInviteMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94102DE54DE2004EEDFB /* RoomInviteMsgCell.swift */; };
		287E94132DE55F79004EEDFB /* RoomItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94122DE55F79004EEDFB /* RoomItemView.swift */; };
		287E94152DE56DD7004EEDFB /* CpListSyVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94142DE56DD7004EEDFB /* CpListSyVC.swift */; };
		287E94172DE5931D004EEDFB /* CpManeuverCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94162DE5931D004EEDFB /* CpManeuverCell.swift */; };
		************************ /* InvterRoomCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* InvterRoomCell.swift */; };
		287E941B2DE5B20D004EEDFB /* SyTextLinkCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E941A2DE5B20D004EEDFB /* SyTextLinkCell.swift */; };
		287E941D2DE5BCA7004EEDFB /* InvterFansPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E941C2DE5BCA7004EEDFB /* InvterFansPopView.swift */; };
		287E948C2DE5D717004EEDFB /* RoomInvterUserSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E948B2DE5D717004EEDFB /* RoomInvterUserSheetView.swift */; };
		287E948E2DE6B123004EEDFB /* RoomLockPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E948D2DE6B123004EEDFB /* RoomLockPopView.swift */; };
		287E94902DE6F0D6004EEDFB /* RoomSettingSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E948F2DE6F0D6004EEDFB /* RoomSettingSheetView.swift */; };
		287E94922DE70196004EEDFB /* RoomChatInputPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94912DE70195004EEDFB /* RoomChatInputPopupView.swift */; };
		287E94942DE7F04F004EEDFB /* UserDisguiseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94932DE7F04F004EEDFB /* UserDisguiseModel.swift */; };
		287E94962DE7FA4E004EEDFB /* UserTitleModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94952DE7FA4E004EEDFB /* UserTitleModel.swift */; };
		287E94992DE8423F004EEDFB /* GiftUserListBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94982DE8423F004EEDFB /* GiftUserListBar.swift */; };
		287E949B2DE87AEA004EEDFB /* BossTiaoJIanSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E949A2DE87AEA004EEDFB /* BossTiaoJIanSheetView.swift */; };
		288548E62D6F3F200047A69D /* PassWordAccountVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548E52D6F3F200047A69D /* PassWordAccountVC.swift */; };
		288548E92D6FF8260047A69D /* T_IMHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548E82D6FF8260047A69D /* T_IMHelper.swift */; };
		288548EB2D7002530047A69D /* LWUserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548EA2D7002530047A69D /* LWUserModel.swift */; };
		288548ED2D7002F30047A69D /* LWUserManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548EC2D7002F30047A69D /* LWUserManger.swift */; };
		288548EF2D70065A0047A69D /* WaitLuanchVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548EE2D70065A0047A69D /* WaitLuanchVC.swift */; };
		288548F12D7019EF0047A69D /* PasswordLoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548F02D7019EF0047A69D /* PasswordLoginVC.swift */; };
		288548F32D7073D80047A69D /* ThirdBindPhoneVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548F22D7073D80047A69D /* ThirdBindPhoneVC.swift */; };
		288548F62D718CE70047A69D /* InfoItemBaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548F52D718CE70047A69D /* InfoItemBaseView.swift */; };
		288548F82D719D9F0047A69D /* BaseBannerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548F72D719D9E0047A69D /* BaseBannerView.swift */; };
		288548FB2D71A2DB0047A69D /* DyHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548FA2D71A2DB0047A69D /* DyHeaderView.swift */; };
		288548FE2D71A5F30047A69D /* BannerModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548FD2D71A5F30047A69D /* BannerModel.swift */; };
		288549002D71B8EB0047A69D /* TopicCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288548FF2D71B8EB0047A69D /* TopicCell.swift */; };
		288549032D71B9B50047A69D /* TopicModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549022D71B9B50047A69D /* TopicModel.swift */; };
		288549052D753BFB0047A69D /* ExpandPushAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549042D753BFB0047A69D /* ExpandPushAnimator.swift */; };
		2885490A2D7541280047A69D /* TopicDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549092D7541280047A69D /* TopicDetailVC.swift */; };
		2885490C2D75522C0047A69D /* CreatePostVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885490B2D75522C0047A69D /* CreatePostVC.swift */; };
		2885490E2D7554B80047A69D /* TopicSelListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885490D2D7554B80047A69D /* TopicSelListVC.swift */; };
		288549102D755D8E0047A69D /* ImageExpandingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885490F2D755D8E0047A69D /* ImageExpandingView.swift */; };
		288549122D75909D0047A69D /* DynamicBaseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549112D75909D0047A69D /* DynamicBaseCell.swift */; };
		288549142D7594A40047A69D /* DynamicBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549132D7594A40047A69D /* DynamicBaseModel.swift */; };
		288549162D7595150047A69D /* DynamicMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549152D7595150047A69D /* DynamicMainVC.swift */; };
		288549182D75954F0047A69D /* DynamicBaseListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549172D75954F0047A69D /* DynamicBaseListVC.swift */; };
		2885491A2D7598760047A69D /* ImageCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549192D7598760047A69D /* ImageCardView.swift */; };
		2885491C2D759C750047A69D /* TagLabelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885491B2D759C750047A69D /* TagLabelView.swift */; };
		2885491E2D7697480047A69D /* DynamicDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885491D2D7697480047A69D /* DynamicDetailVC.swift */; };
		288549202D7698BF0047A69D /* DynamicDetailHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885491F2D7698BF0047A69D /* DynamicDetailHeaderView.swift */; };
		288549222D76D9BD0047A69D /* RemarkTopCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549212D76D9BC0047A69D /* RemarkTopCell.swift */; };
		288549242D76E04B0047A69D /* CommentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549232D76E04B0047A69D /* CommentModel.swift */; };
		288549262D76E0F10047A69D /* CommentOneCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549252D76E0F10047A69D /* CommentOneCell.swift */; };
		288549282D76FA030047A69D /* String_Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549272D76FA020047A69D /* String_Ext.swift */; };
		2885492D2D77248C0047A69D /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885492C2D77248C0047A69D /* LocationManager.swift */; };
		288549312D7729A40047A69D /* le_city.json in Resources */ = {isa = PBXBuildFile; fileRef = 288549302D7729A40047A69D /* le_city.json */; };
		288549332D77EB330047A69D /* DynamicFollowVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549322D77EB330047A69D /* DynamicFollowVC.swift */; };
		288549352D77EE780047A69D /* FollowNodataView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549342D77EE780047A69D /* FollowNodataView.swift */; };
		288549372D77FAC20047A69D /* DynamicNewListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549362D77FAC20047A69D /* DynamicNewListVC.swift */; };
		288549392D77FB380047A69D /* DynamicLocListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549382D77FB380047A69D /* DynamicLocListVC.swift */; };
		2885493B2D7829180047A69D /* loading2.json in Resources */ = {isa = PBXBuildFile; fileRef = 2885493A2D7829180047A69D /* loading2.json */; };
		2885493D2D782AF80047A69D /* LWLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885493C2D782AF80047A69D /* LWLoadingView.swift */; };
		2885493F2D782C520047A69D /* LodingRefeshHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885493E2D782C520047A69D /* LodingRefeshHeaderView.swift */; };
		288549412D783A390047A69D /* VoiceBaseCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549402D783A390047A69D /* VoiceBaseCardView.swift */; };
		288549432D783DD80047A69D /* SVGAnimationPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549422D783DD80047A69D /* SVGAnimationPlayer.swift */; };
		288549452D7843330047A69D /* dy_voicePlay.svga in Resources */ = {isa = PBXBuildFile; fileRef = 288549442D7843330047A69D /* dy_voicePlay.svga */; };
		288549482D7846FD0047A69D /* MediaPlaybackManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549472D7846FD0047A69D /* MediaPlaybackManager.swift */; };
		2885494B2D787A490047A69D /* DyInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885494A2D787A490047A69D /* DyInputView.swift */; };
		2885494D2D7937E30047A69D /* emoji_mapping.json in Resources */ = {isa = PBXBuildFile; fileRef = 2885494C2D7937E30047A69D /* emoji_mapping.json */; };
		288549502D7938300047A69D /* EmojiModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885494F2D7938300047A69D /* EmojiModel.swift */; };
		288549522D7938530047A69D /* EmojiManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549512D7938530047A69D /* EmojiManger.swift */; };
		288549542D793C370047A69D /* EmojiKeyboardVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549532D793C370047A69D /* EmojiKeyboardVC.swift */; };
		288549562D793E0E0047A69D /* EmojiMainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 288549552D793E0E0047A69D /* EmojiMainView.swift */; };
		2885499E2D79BB6D0047A69D /* EmojiTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2885499D2D79BB6D0047A69D /* EmojiTextView.swift */; };
		289F9E042DC9F7D300D7522D /* RoomToolSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E032DC9F7D300D7522D /* RoomToolSheetView.swift */; };
		289F9E3A2DCA027D00D7522D /* VoiceRoomActivityCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E392DCA027D00D7522D /* VoiceRoomActivityCell.swift */; };
		289F9E3C2DCA02BD00D7522D /* VoiceRoomActivitySheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E3B2DCA02BD00D7522D /* VoiceRoomActivitySheetView.swift */; };
		289F9E892DCB0C9000D7522D /* BossAttackFloatingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E882DCB0C9000D7522D /* BossAttackFloatingView.swift */; };
		289F9E8F2DCB0D7D00D7522D /* boss_max_aida.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9E8B2DCB0D7D00D7522D /* boss_max_aida.svga */; };
		289F9E902DCB0D7D00D7522D /* boss_mini_aida.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9E8D2DCB0D7D00D7522D /* boss_mini_aida.svga */; };
		289F9E912DCB0D7D00D7522D /* boss_mini_xh.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9E8E2DCB0D7D00D7522D /* boss_mini_xh.svga */; };
		289F9E922DCB0D7D00D7522D /* boss_max_xh.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9E8C2DCB0D7D00D7522D /* boss_max_xh.svga */; };
		289F9E942DCC4E7000D7522D /* BoosMainSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E932DCC4E7000D7522D /* BoosMainSheetView.swift */; };
		289F9E972DCC59F000D7522D /* BossRecordTickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E962DCC59F000D7522D /* BossRecordTickerView.swift */; };
		289F9E9A2DCC5A4100D7522D /* BossRecordRotation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E992DCC5A4100D7522D /* BossRecordRotation.swift */; };
		289F9E9C2DCC5C6400D7522D /* boss_fazhen.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9E9B2DCC5C6400D7522D /* boss_fazhen.svga */; };
		289F9E9E2DCC929200D7522D /* gaojiMofa.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 289F9E9D2DCC929200D7522D /* gaojiMofa.mp4 */; };
		289F9EA02DCC98E100D7522D /* CustomProgressBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9E9F2DCC98E100D7522D /* CustomProgressBar.swift */; };
		289F9EA62DCC9F4200D7522D /* boss_box.zip in Resources */ = {isa = PBXBuildFile; fileRef = 289F9EA52DCC9F4200D7522D /* boss_box.zip */; };
		289F9EA82DCCAB6600D7522D /* putongMF.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 289F9EA72DCCAB6600D7522D /* putongMF.mp4 */; };
		289F9EAB2DCCAC9F00D7522D /* boss_attPath.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9EA92DCCAC9F00D7522D /* boss_attPath.svga */; };
		289F9EAC2DCCAC9F00D7522D /* boss_boom.svga in Resources */ = {isa = PBXBuildFile; fileRef = 289F9EAA2DCCAC9F00D7522D /* boss_boom.svga */; };
		289F9EAE2DCCD3B600D7522D /* BossBuyBookSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9EAD2DCCD3B600D7522D /* BossBuyBookSheetView.swift */; };
		289F9F692DCDA50F00D7522D /* BossValueView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9F682DCDA50F00D7522D /* BossValueView.swift */; };
		289F9F6B2DCDEE6000D7522D /* StackedNotificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289F9F6A2DCDEE6000D7522D /* StackedNotificationView.swift */; };
		28AFCD5C2DB120C2003CE291 /* HotRoomMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCD5B2DB120C2003CE291 /* HotRoomMainVC.swift */; };
		28AFCDAA2DB5E41B003CE291 /* RoomSnapshot.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDA92DB5E41B003CE291 /* RoomSnapshot.swift */; };
		28AFCDAD2DB5F740003CE291 /* RoomSeatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDAC2DB5F740003CE291 /* RoomSeatView.swift */; };
		28AFCDAF2DB5F790003CE291 /* RoomContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDAE2DB5F790003CE291 /* RoomContainerView.swift */; };
		28AFCDB42DB654C2003CE291 /* SideMenuView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDB32DB654C2003CE291 /* SideMenuView.swift */; };
		28AFCDB62DB65ABE003CE291 /* RoomInfoTopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDB52DB65ABE003CE291 /* RoomInfoTopView.swift */; };
		28AFCDB92DB66717003CE291 /* RoomDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDB82DB66717003CE291 /* RoomDetailModel.swift */; };
		28AFCDBB2DB72430003CE291 /* AudioWaveView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDBA2DB72430003CE291 /* AudioWaveView.swift */; };
		28AFCDBD2DB73070003CE291 /* CreateRoomVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDBC2DB73070003CE291 /* CreateRoomVC.swift */; };
		28AFCDBF2DB730C3003CE291 /* RoomTypeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDBE2DB730C3003CE291 /* RoomTypeCell.swift */; };
		28AFCDC12DB73945003CE291 /* RoomTypeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDC02DB73945003CE291 /* RoomTypeModel.swift */; };
		28AFCDC32DB73AA4003CE291 /* GameTypeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDC22DB73AA4003CE291 /* GameTypeCell.swift */; };
		28AFCDC52DB791E5003CE291 /* AuthenticationPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDC42DB791E5003CE291 /* AuthenticationPopupView.swift */; };
		28AFCDFF2DB79CD4003CE291 /* RoomFloatingWindow.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCDFE2DB79CD4003CE291 /* RoomFloatingWindow.swift */; };
		28AFCE012DB7A7BF003CE291 /* RoomFloatingWindowManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE002DB7A7BF003CE291 /* RoomFloatingWindowManager.swift */; };
		28AFCE032DB7B776003CE291 /* RoomBgView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE022DB7B776003CE291 /* RoomBgView.swift */; };
		28AFCE052DB88728003CE291 /* AvatarFrameView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE042DB88728003CE291 /* AvatarFrameView.swift */; };
		28AFCE072DB8DE71003CE291 /* RoomMsgManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE062DB8DE71003CE291 /* RoomMsgManger.swift */; };
		28AFCE092DB9CA64003CE291 /* TickHub.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE082DB9CA64003CE291 /* TickHub.swift */; };
		28AFCE792DBA123A003CE291 /* RoomViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE782DBA123A003CE291 /* RoomViewModel.swift */; };
		28AFCE7B2DBA127D003CE291 /* RoomPublicMessageVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE7A2DBA127D003CE291 /* RoomPublicMessageVC.swift */; };
		28AFCE7D2DBA12A3003CE291 /* RoomBaseMsgModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE7C2DBA12A3003CE291 /* RoomBaseMsgModel.swift */; };
		28AFCE822DBA2D56003CE291 /* RoomSeatConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE812DBA2D56003CE291 /* RoomSeatConfiguration.swift */; };
		28AFCE842DBA4395003CE291 /* RoomMsgType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE832DBA4395003CE291 /* RoomMsgType.swift */; };
		28AFCE862DBA45A2003CE291 /* RoomMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCE852DBA45A2003CE291 /* RoomMessageCell.swift */; };
		28AFCEF42DBB6882003CE291 /* RoomNavUserView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCEF32DBB6882003CE291 /* RoomNavUserView.swift */; };
		28AFCF112DBB7297003CE291 /* RoomUserInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF102DBB7297003CE291 /* RoomUserInfoModel.swift */; };
		28AFCF3C2DBB9353003CE291 /* RoomChatBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF3B2DBB9353003CE291 /* RoomChatBarView.swift */; };
		28AFCF472DBDC96E003CE291 /* CircleProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF462DBDC96E003CE291 /* CircleProgressView.swift */; };
		28AFCF492DBDCD3A003CE291 /* LuckSproutButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF482DBDCD3A003CE291 /* LuckSproutButton.swift */; };
		28AFCF982DBE3AA2003CE291 /* RoomInfoCardSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF972DBE3AA2003CE291 /* RoomInfoCardSheetView.swift */; };
		28AFCF9D2DBF34CB003CE291 /* AppConfigManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF9C2DBF34CB003CE291 /* AppConfigManger.swift */; };
		28AFCF9F2DBF5A03003CE291 /* TagListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCF9E2DBF5A03003CE291 /* TagListView.swift */; };
		28AFCFCC2DBF8B49003CE291 /* SeatActionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCFCB2DBF8B49003CE291 /* SeatActionManager.swift */; };
		28AFCFDB2DC0CD91003CE291 /* RoomDetailSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28AFCFDA2DC0CD91003CE291 /* RoomDetailSheetView.swift */; };
		28B931C42D7A8F6000DE4F8E /* ChatInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B931C32D7A8F6000DE4F8E /* ChatInputView.swift */; };
		28B931C62D7ACD9900DE4F8E /* DyPostInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B931C52D7ACD9900DE4F8E /* DyPostInputView.swift */; };
		28B931C82D7AF12800DE4F8E /* VoicePostView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B931C72D7AF12800DE4F8E /* VoicePostView.swift */; };
		28B931CA2D7AF93900DE4F8E /* AudioRecordSession.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B931C92D7AF93900DE4F8E /* AudioRecordSession.swift */; };
		28B932342D7ED6F900DE4F8E /* DySelUserlistVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B932332D7ED6F900DE4F8E /* DySelUserlistVC.swift */; };
		28B932362D7ED83100DE4F8E /* DySelUserCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B932352D7ED83100DE4F8E /* DySelUserCell.swift */; };
		28B932382D7EFCCA00DE4F8E /* DyImagePickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B932372D7EFCCA00DE4F8E /* DyImagePickerView.swift */; };
		28B9323A2D801D8900DE4F8E /* RateLimitManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B932392D801D8900DE4F8E /* RateLimitManager.swift */; };
		28B932942D81367B00DE4F8E /* LWJumpManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B932932D81367B00DE4F8E /* LWJumpManger.swift */; };
		28B932D72D81602000DE4F8E /* MessagePopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28B932D62D81602000DE4F8E /* MessagePopupView.swift */; };
		28BB749E2DD19D44004B9997 /* BossJpMainSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB749D2DD19D44004B9997 /* BossJpMainSheetView.swift */; };
		28BB74A02DD1CDCF004B9997 /* BossJpListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB749F2DD1CDCF004B9997 /* BossJpListVC.swift */; };
		28BB74A22DD1D31F004B9997 /* JiangPingCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB74A12DD1D31F004B9997 /* JiangPingCell.swift */; };
		28BB74A42DD1D348004B9997 /* JingLiCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB74A32DD1D348004B9997 /* JingLiCell.swift */; };
		28BB74A62DD1E925004B9997 /* BossMoreTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB74A52DD1E925004B9997 /* BossMoreTextView.swift */; };
		28BB74A82DD1ED0F004B9997 /* BossGoodsdhSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB74A72DD1ED0F004B9997 /* BossGoodsdhSheetView.swift */; };
		28BB74AA2DD1F7F1004B9997 /* BossGoodsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB74A92DD1F7F1004B9997 /* BossGoodsCell.swift */; };
		28BB74AC2DD1FDDF004B9997 /* BossMyRateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28BB74AB2DD1FDDF004B9997 /* BossMyRateView.swift */; };
		28C1ED0B2DD72CE1006F0442 /* TuPoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED0A2DD72CE1006F0442 /* TuPoView.swift */; };
		28C1ED0D2DD72CF6006F0442 /* UpgradeConditionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED0C2DD72CF6006F0442 /* UpgradeConditionView.swift */; };
		28C1ED102DD73E68006F0442 /* SkillGoodsMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED0F2DD73E68006F0442 /* SkillGoodsMainVC.swift */; };
		28C1ED122DD73E71006F0442 /* SkillGoodsListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED112DD73E71006F0442 /* SkillGoodsListVC.swift */; };
		28C1ED182DDAC203006F0442 /* CurrencyBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED162DDAC203006F0442 /* CurrencyBottomView.swift */; };
		28C1ED1A2DDACDF0006F0442 /* GoodsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED192DDACDF0006F0442 /* GoodsCell.swift */; };
		28C1ED1C2DDADF9E006F0442 /* GoodsBuyPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED1B2DDADF9E006F0442 /* GoodsBuyPopView.swift */; };
		28C1ED1F2DDB2E1E006F0442 /* RoomTextMsgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C1ED1E2DDB2E1E006F0442 /* RoomTextMsgCell.swift */; };
		28E21A6C2D6C68280007ED12 /* EnvManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A6B2D6C68280007ED12 /* EnvManager.swift */; };
		28E21A6F2D6C687F0007ED12 /* AppTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A6E2D6C687F0007ED12 /* AppTool.swift */; };
		28E21A712D6C6AF30007ED12 /* ApiResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A702D6C6AF30007ED12 /* ApiResponse.swift */; };
		28E21A732D6C6C340007ED12 /* AppConfigUI_Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A722D6C6C340007ED12 /* AppConfigUI_Ext.swift */; };
		28E21A752D6C6F530007ED12 /* ApiService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A742D6C6F530007ED12 /* ApiService.swift */; };
		28E21A772D6C706B0007ED12 /* NetworkUtility.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A762D6C706B0007ED12 /* NetworkUtility.swift */; };
		28E21A7A2D6C7DAC0007ED12 /* SmAntiFraud.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A792D6C7DAC0007ED12 /* SmAntiFraud.xcframework */; };
		28E21A822D6C802B0007ED12 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A812D6C802B0007ED12 /* IOKit.framework */; };
		28E21A842D6C803D0007ED12 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A832D6C803D0007ED12 /* CoreLocation.framework */; };
		28E21A8D2D6D6BAA0007ED12 /* Network.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A8C2D6D6BAA0007ED12 /* Network.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		28E21A912D6D6BF40007ED12 /* YTXOperators.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A902D6D6BF40007ED12 /* YTXOperators.framework */; };
		28E21A922D6D6BF40007ED12 /* ATAuthSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A8E2D6D6BF40007ED12 /* ATAuthSDK.framework */; };
		28E21A932D6D6BF40007ED12 /* YTXMonitor.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28E21A8F2D6D6BF40007ED12 /* YTXMonitor.framework */; };
		28E21A962D6D6C1B0007ED12 /* QuickManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A952D6D6C1B0007ED12 /* QuickManger.swift */; };
		28E21A982D6D6C8B0007ED12 /* ATAuthSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 28E21A972D6D6C8B0007ED12 /* ATAuthSDK.bundle */; };
		28E21A9B2D6D98590007ED12 /* PrivacySettingsSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A9A2D6D98590007ED12 /* PrivacySettingsSheetView.swift */; };
		28E21A9D2D6D9B550007ED12 /* Notification_Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A9C2D6D9B550007ED12 /* Notification_Ext.swift */; };
		28E21A9F2D6D9E510007ED12 /* ThirdQuickView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21A9E2D6D9E510007ED12 /* ThirdQuickView.swift */; };
		28E21AA22D6DA0AC0007ED12 /* AliyunOssHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AA12D6DA0AC0007ED12 /* AliyunOssHelper.swift */; };
		28E21AA42D6DAC030007ED12 /* ProfileSetupVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AA32D6DAC030007ED12 /* ProfileSetupVC.swift */; };
		28E21AA72D6DC1470007ED12 /* QuickLoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AA62D6DC1470007ED12 /* QuickLoginView.swift */; };
		28E21AA92D6DD7100007ED12 /* SendCodeVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AA82D6DD7100007ED12 /* SendCodeVC.swift */; };
		28E21AAC2D6EB1930007ED12 /* LoginUserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AAB2D6EB1930007ED12 /* LoginUserModel.swift */; };
		28E21AAE2D6EB5C00007ED12 /* SelectAccountVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AAD2D6EB5C00007ED12 /* SelectAccountVC.swift */; };
		28E21AB02D6EED690007ED12 /* PasswordSettingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AAF2D6EED690007ED12 /* PasswordSettingVC.swift */; };
		28E21AB22D6EF1AD0007ED12 /* FeedbackVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E21AB12D6EF1AD0007ED12 /* FeedbackVC.swift */; };
		28E21AB52D6F11BB0007ED12 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 28E21AB42D6F11BB0007ED12 /* PrivacyInfo.xcprivacy */; };
		28E785312DC9AD440092CED9 /* AudienceRoomClosedVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E785302DC9AD440092CED9 /* AudienceRoomClosedVC.swift */; };
		28E785332DC9BA590092CED9 /* RoomSummaryVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28E785322DC9BA590092CED9 /* RoomSummaryVC.swift */; };
		28EAA6B42D9E7CC0001600E8 /* MatchQuestionModalView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28EAA6B32D9E7CC0001600E8 /* MatchQuestionModalView.swift */; };
		28EB87E92DEED09200D389FB /* RoomEditTitleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28EB87E82DEED09200D389FB /* RoomEditTitleView.swift */; };
		28EB87EB2DEFEB8000D389FB /* RoomTypeSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28EB87EA2DEFEB8000D389FB /* RoomTypeSelectionView.swift */; };
		28EB87ED2DF0214200D389FB /* RoomSeatDegreeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28EB87EC2DF0214200D389FB /* RoomSeatDegreeView.swift */; };
		28EB87EF2DF0440000D389FB /* HotInfoPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28EB87EE2DF0440000D389FB /* HotInfoPopupView.swift */; };
		28EB87F22DF1383D00D389FB /* VolumePopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28EB87F02DF1383D00D389FB /* VolumePopupView.swift */; };
		28F6433F2D93F2BE00487E43 /* ChatDetailNavView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F6433E2D93F2BE00487E43 /* ChatDetailNavView.swift */; };
		28F643412D93F8D200487E43 /* ChatDetailUserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643402D93F8D200487E43 /* ChatDetailUserModel.swift */; };
		28F643DD2D9535F700487E43 /* SendMsg-Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643DC2D9535F700487E43 /* SendMsg-Ext.swift */; };
		28F643E92D953B0700487E43 /* AudioRecordingProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643E82D953B0700487E43 /* AudioRecordingProgressView.swift */; };
		28F643EB2D953B1B00487E43 /* AudioVisualizerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643EA2D953B1B00487E43 /* AudioVisualizerView.swift */; };
		28F643ED2D95656D00487E43 /* TacitOptionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643EC2D95656D00487E43 /* TacitOptionCell.swift */; };
		28F643EF2D95664800487E43 /* TacitModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643EE2D95664800487E43 /* TacitModel.swift */; };
		28F643F12D967DBC00487E43 /* TacitOptionCardSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28F643F02D967DBC00487E43 /* TacitOptionCardSheetView.swift */; };
		421847C4AAFF6E1248DC2A86 /* Pods_YINDONG.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6F053B6ABFE3B6136BECD374 /* Pods_YINDONG.framework */; };
		954188182D0558990043071D /* MessageStorage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954188172D0558990043071D /* MessageStorage.swift */; };
		9541881B2D055ABF0043071D /* ChatAudioManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954188192D055ABF0043071D /* ChatAudioManager.swift */; };
		9541881D2D055B530043071D /* MessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9541881C2D055B520043071D /* MessageCell.swift */; };
		954188202D0561F90043071D /* ChatUserManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9541881E2D0561F90043071D /* ChatUserManager.swift */; };
		954188212D0561F90043071D /* BlacklistManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9541881F2D0561F90043071D /* BlacklistManager.swift */; };
		954188242D05762F0043071D /* MicrophonePositionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954188222D05762F0043071D /* MicrophonePositionView.swift */; };
		954188252D05762F0043071D /* PublicMessageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954188232D05762F0043071D /* PublicMessageView.swift */; };
		954188272D0579200043071D /* WebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954188262D0579200043071D /* WebViewController.swift */; };
		9567954D2CFF528C00F03EF8 /* ChatListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9567954B2CFF528C00F03EF8 /* ChatListVC.swift */; };
		9567954E2CFF528C00F03EF8 /* ChatDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9567954C2CFF528C00F03EF8 /* ChatDetailVC.swift */; };
		956795552CFF529300F03EF8 /* ChatListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9567954F2CFF529300F03EF8 /* ChatListCell.swift */; };
		956795572CFF529300F03EF8 /* ConversationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956795522CFF529300F03EF8 /* ConversationModel.swift */; };
		956795582CFF529300F03EF8 /* MessageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956795532CFF529300F03EF8 /* MessageModel.swift */; };
		9567955C2CFF711100F03EF8 /* LoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956795592CFF711100F03EF8 /* LoginVC.swift */; };
		956795602CFF711F00F03EF8 /* UserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9567955E2CFF711F00F03EF8 /* UserModel.swift */; };
		956795642CFF72D100F03EF8 /* BlacklistVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956795612CFF72D000F03EF8 /* BlacklistVC.swift */; };
		956795652CFF72D100F03EF8 /* SettingsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956795622CFF72D000F03EF8 /* SettingsVC.swift */; };
		959B2ACE2CE840A90047E096 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2ACD2CE840A90047E096 /* AppDelegate.swift */; };
		959B2AD72CE840AA0047E096 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 959B2AD62CE840AA0047E096 /* Assets.xcassets */; };
		959B2ADA2CE840AA0047E096 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 959B2AD92CE840AA0047E096 /* Base */; };
		959B2AE42CE843010047E096 /* MainTabBarVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AE32CE843010047E096 /* MainTabBarVC.swift */; };
		959B2AE62CE844420047E096 /* YinDBaseVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AE52CE844420047E096 /* YinDBaseVC.swift */; };
		959B2AF02CE84AFD0047E096 /* UniversalPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AEF2CE84AFD0047E096 /* UniversalPopupView.swift */; };
		959B2AF82CE84D6D0047E096 /* MusicTheoryQuizViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AF72CE84D6D0047E096 /* MusicTheoryQuizViewController.swift */; };
		959B2AFA2CE84E760047E096 /* InstrumentCollectionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AF92CE84E760047E096 /* InstrumentCollectionViewController.swift */; };
		959B2AFD2CE851890047E096 /* TCApAudioRecordingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AFC2CE851890047E096 /* TCApAudioRecordingManager.swift */; };
		959B2B002CE8520C0047E096 /* AudioRecordingHUDView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2AFF2CE8520C0047E096 /* AudioRecordingHUDView.swift */; };
		959B2B032CE853440047E096 /* RecordingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B022CE853440047E096 /* RecordingViewController.swift */; };
		959B2B052CE85D120047E096 /* ChatMainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B042CE85D120047E096 /* ChatMainVC.swift */; };
		959B2B072CE85D960047E096 /* SyteamListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B062CE85D960047E096 /* SyteamListVC.swift */; };
		959B2B0A2CE862770047E096 /* MineVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B092CE862770047E096 /* MineVC.swift */; };
		959B2B0C2CE880250047E096 /* RecordingTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B0B2CE880250047E096 /* RecordingTableViewCell.swift */; };
		959B2B0E2CE8803C0047E096 /* RecordingsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B0D2CE8803C0047E096 /* RecordingsViewController.swift */; };
		959B2B172CE8B4320047E096 /* MusicEvent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B162CE8B4320047E096 /* MusicEvent.swift */; };
		959B2B192CE8B44F0047E096 /* ActivityTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B182CE8B44F0047E096 /* ActivityTableViewCell.swift */; };
		959B2B1B2CE8B4630047E096 /* MusicActListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B1A2CE8B4630047E096 /* MusicActListVC.swift */; };
		959B2B1D2CE8B4740047E096 /* MusicEventPopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959B2B1C2CE8B4740047E096 /* MusicEventPopView.swift */; };
		95DAA59D2D2D6CA500126D4E /* PostModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA59C2D2D6CA500126D4E /* PostModel.swift */; };
		95DAA5A22D2D6CAE00126D4E /* PostCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA59E2D2D6CAE00126D4E /* PostCell.swift */; };
		95DAA5A32D2D6CAE00126D4E /* SocialFeedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5A02D2D6CAE00126D4E /* SocialFeedViewController.swift */; };
		95DAA5A52D2D6CCF00126D4E /* TwoMainViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5A42D2D6CCF00126D4E /* TwoMainViewController.swift */; };
		95DAA5A82D2D70EA00126D4E /* CreatePostViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5A62D2D70EA00126D4E /* CreatePostViewController.swift */; };
		95DAA5A92D2D70EA00126D4E /* PostDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5A72D2D70EA00126D4E /* PostDetailViewController.swift */; };
		95DAA5AB2D2D730400126D4E /* MusicianListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5AA2D2D730400126D4E /* MusicianListViewController.swift */; };
		95DAA5AD2D2D730A00126D4E /* FeedListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5AC2D2D730A00126D4E /* FeedListViewController.swift */; };
		95DAA5AF2D2D731200126D4E /* MusicianCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5AE2D2D731200126D4E /* MusicianCell.swift */; };
		95DAA5B12D2D732300126D4E /* MusicianModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5B02D2D732300126D4E /* MusicianModel.swift */; };
		95DAA5B32D2D750D00126D4E /* UserProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5B22D2D750D00126D4E /* UserProfileViewController.swift */; };
		95DAA5B52D2D789600126D4E /* ReportModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5B42D2D789600126D4E /* ReportModel.swift */; };
		95DAA5B72D2D78A500126D4E /* ReportActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5B62D2D78A500126D4E /* ReportActionSheet.swift */; };
		95DAA5B92D2D79E100126D4E /* BlockedUserManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DAA5B82D2D79E100126D4E /* BlockedUserManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		281B40432E013D6100939041 /* skill_level_100.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_100.lottie; sourceTree = "<group>"; };
		281B40452E01838500939041 /* PkResultPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PkResultPopView.swift; sourceTree = "<group>"; };
		281B40482E018FC400939041 /* skill_level_45.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_45.lottie; sourceTree = "<group>"; };
		281B40492E018FC400939041 /* skill_level_50.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_50.lottie; sourceTree = "<group>"; };
		281B404A2E018FC400939041 /* skill_level_55.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_55.lottie; sourceTree = "<group>"; };
		281B404B2E018FC400939041 /* skill_level_60.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_60.lottie; sourceTree = "<group>"; };
		281B404C2E018FC400939041 /* skill_level_65.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_65.lottie; sourceTree = "<group>"; };
		281B404D2E018FC400939041 /* skill_level_70.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_70.lottie; sourceTree = "<group>"; };
		281B404E2E018FC400939041 /* skill_level_75.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_75.lottie; sourceTree = "<group>"; };
		281B404F2E018FC400939041 /* skill_level_80.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_80.lottie; sourceTree = "<group>"; };
		281B40502E018FC400939041 /* skill_level_85.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_85.lottie; sourceTree = "<group>"; };
		281B40512E018FC400939041 /* skill_level_90.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_90.lottie; sourceTree = "<group>"; };
		281B40522E018FC400939041 /* skill_level_95.lottie */ = {isa = PBXFileReference; lastKnownFileType = file; path = skill_level_95.lottie; sourceTree = "<group>"; };
		281B40602E02AC8C00939041 /* EnvConfigVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnvConfigVC.swift; sourceTree = "<group>"; };
		281B40622E02ACFB00939041 /* CustomConfigVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomConfigVC.swift; sourceTree = "<group>"; };
		281B40652E02D39600939041 /* MansionMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionMainVC.swift; sourceTree = "<group>"; };
		281B40672E02DA1400939041 /* work_new_select.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = work_new_select.svga; sourceTree = "<group>"; };
		281B406B2E02E1FC00939041 /* MansionMainModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionMainModel.swift; sourceTree = "<group>"; };
		281B406D2E02E94B00939041 /* MansionAttendantCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionAttendantCell.swift; sourceTree = "<group>"; };
		281B406E2E02E94B00939041 /* MansionAttendantSectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionAttendantSectionView.swift; sourceTree = "<group>"; };
		281B406F2E02E94B00939041 /* MansionHeaderInfoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionHeaderInfoView.swift; sourceTree = "<group>"; };
		281B40702E02E94B00939041 /* MansionMasterSectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionMasterSectionView.swift; sourceTree = "<group>"; };
		281B40712E02E94B00939041 /* MansionWorkSectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionWorkSectionView.swift; sourceTree = "<group>"; };
		281B40772E03E9DC00939041 /* MansionTaskSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionTaskSheetView.swift; sourceTree = "<group>"; };
		281B40792E042FC500939041 /* MansionMangerListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionMangerListVC.swift; sourceTree = "<group>"; };
		281B407B2E043C3800939041 /* MansionSytemPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionSytemPopView.swift; sourceTree = "<group>"; };
		281B407D2E050EB800939041 /* MansionHisPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MansionHisPopView.swift; sourceTree = "<group>"; };
		281B40802E05583900939041 /* RuneDisplayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RuneDisplayView.swift; sourceTree = "<group>"; };
		281B40822E08E10B00939041 /* RuneDisplayInfoListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RuneDisplayInfoListView.swift; sourceTree = "<group>"; };
		281B40842E09614D00939041 /* TuLongRewardWithNameCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRewardWithNameCell.swift; sourceTree = "<group>"; };
		281B40862E0A40B500939041 /* MineUserVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MineUserVC.swift; sourceTree = "<group>"; };
		281B40882E0A436100939041 /* MenuItemManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuItemManager.swift; sourceTree = "<group>"; };
		281B40892E0A436100939041 /* MenuItemModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuItemModel.swift; sourceTree = "<group>"; };
		281B408D2E0A43CC00939041 /* FunctionGridView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FunctionGridView.swift; sourceTree = "<group>"; };
		281B408E2E0A43CC00939041 /* ServiceMenuView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServiceMenuView.swift; sourceTree = "<group>"; };
		281B408F2E0A43CC00939041 /* UserHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserHeaderView.swift; sourceTree = "<group>"; };
		281C6FC82DFFFB3D002F9D99 /* UseSkillCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UseSkillCell.swift; sourceTree = "<group>"; };
		281C6FCA2DFFFF43002F9D99 /* skill_catch_fail.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_catch_fail.json; sourceTree = "<group>"; };
		281C6FCB2DFFFF43002F9D99 /* skill_catch_success.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_catch_success.json; sourceTree = "<group>"; };
		281C6FCC2DFFFF43002F9D99 /* skill_foot_fail.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_foot_fail.json; sourceTree = "<group>"; };
		281C6FCD2DFFFF43002F9D99 /* skill_foot_success.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_foot_success.json; sourceTree = "<group>"; };
		281C6FCE2DFFFF43002F9D99 /* skill_hole_fail.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_hole_fail.json; sourceTree = "<group>"; };
		281C6FCF2DFFFF43002F9D99 /* skill_hole_success.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_hole_success.json; sourceTree = "<group>"; };
		281C6FD02DFFFF43002F9D99 /* skill_kiss_fail.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_kiss_fail.json; sourceTree = "<group>"; };
		281C6FD12DFFFF43002F9D99 /* skill_kiss_success.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_kiss_success.json; sourceTree = "<group>"; };
		281C6FD22DFFFF43002F9D99 /* skill_talk_fail.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_talk_fail.json; sourceTree = "<group>"; };
		281C6FD32DFFFF43002F9D99 /* skill_talk_success.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = skill_talk_success.json; sourceTree = "<group>"; };
		281C6FE02E000389002F9D99 /* GameUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameUtils.swift; sourceTree = "<group>"; };
		282188912E0E2F7400362550 /* ScreenManagement.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScreenManagement.swift; sourceTree = "<group>"; };
		2821891E2E0E8E6200362550 /* ZuoJiaMangement.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZuoJiaMangement.swift; sourceTree = "<group>"; };
		2825A3B52DF6788F00EE425C /* RedPacketRainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RedPacketRainView.swift; sourceTree = "<group>"; };
		2825A3B72DF6B66C00EE425C /* TuLongMainSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongMainSheetView.swift; sourceTree = "<group>"; };
		2825A3BA2DF6B75B00EE425C /* TaskCarousel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskCarousel.swift; sourceTree = "<group>"; };
		2825A3BD2DF6BF1400EE425C /* RoomTaskModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomTaskModel.swift; sourceTree = "<group>"; };
		2825A3BF2DF6BFD100EE425C /* TLCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TLCell.swift; sourceTree = "<group>"; };
		2825A3C12DF6D14700EE425C /* dragon_enter.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = dragon_enter.mp4; sourceTree = "<group>"; };
		2825A3C22DF6D14700EE425C /* dragon_normal.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = dragon_normal.mp4; sourceTree = "<group>"; };
		2825A3C52DF7C8A200EE425C /* SheetWkWebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SheetWkWebView.swift; sourceTree = "<group>"; };
		2825A3C72DF7E0DB00EE425C /* DragonHurt.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = DragonHurt.ttf; sourceTree = "<group>"; };
		2825A3C92DF835C700EE425C /* TuLongRedLuckPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRedLuckPopView.swift; sourceTree = "<group>"; };
		2825A3CB2DF8397000EE425C /* CenteredCollectionViewFlowLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CenteredCollectionViewFlowLayout.swift; sourceTree = "<group>"; };
		2825A3CD2DF8409100EE425C /* AlibabaPuHuiTi_2_115_Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = AlibabaPuHuiTi_2_115_Black.ttf; sourceTree = "<group>"; };
		2825A3CF2DF852D500EE425C /* TuLongRecordSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRecordSheetView.swift; sourceTree = "<group>"; };
		2825A3D12DF8571A00EE425C /* TuLongRecordModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRecordModel.swift; sourceTree = "<group>"; };
		2825A3D42DF8572500EE425C /* TuLongRecordCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRecordCell.swift; sourceTree = "<group>"; };
		2825A3D62DF8572D00EE425C /* TuLongRecordEmptyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRecordEmptyView.swift; sourceTree = "<group>"; };
		2825A3D82DF8617F00EE425C /* TuLongAttMainSheetV.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongAttMainSheetV.swift; sourceTree = "<group>"; };
		2825A3DA2DF862E500EE425C /* TuLongBaseAttRankListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongBaseAttRankListVC.swift; sourceTree = "<group>"; };
		2825A3DC2DF91B2D00EE425C /* TuLongRankModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRankModel.swift; sourceTree = "<group>"; };
		2825A3DE2DF91B7E00EE425C /* TuLongMyRankBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongMyRankBarView.swift; sourceTree = "<group>"; };
		2825A3DF2DF91B7E00EE425C /* TuLongRankCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRankCell.swift; sourceTree = "<group>"; };
		2825A3E02DF91B7E00EE425C /* TuLongRewardCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRewardCell.swift; sourceTree = "<group>"; };
		2825A3E42DF9281800EE425C /* TuLongRoomRankListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRoomRankListVC.swift; sourceTree = "<group>"; };
		2825A3E62DF92DC700EE425C /* TuLongRoomRankItemVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongRoomRankItemVC.swift; sourceTree = "<group>"; };
		2825A3E82DF9527F00EE425C /* UniversalTickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UniversalTickerView.swift; sourceTree = "<group>"; };
		2825A3EC2DF952BC00EE425C /* TuLongTickerManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongTickerManager.swift; sourceTree = "<group>"; };
		2825A3EF2DF95AD500EE425C /* TuLongAttCarouseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongAttCarouseView.swift; sourceTree = "<group>"; };
		2825A3F12DF96DEC00EE425C /* TulongSettlementPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TulongSettlementPopView.swift; sourceTree = "<group>"; };
		2825A3F32DF9721000EE425C /* TuLongSettlementCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuLongSettlementCell.swift; sourceTree = "<group>"; };
		2825A3F52DF99A1D00EE425C /* TaskCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskCell.swift; sourceTree = "<group>"; };
		2825A3F72DFA650400EE425C /* TaskSurePopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskSurePopView.swift; sourceTree = "<group>"; };
		2825A3F92DFA844700EE425C /* ExitRoomTaskPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExitRoomTaskPopView.swift; sourceTree = "<group>"; };
		2825A3FB2DFAAB6E00EE425C /* AdvertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvertView.swift; sourceTree = "<group>"; };
		2825A3FD2DFAB40900EE425C /* ComboButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComboButton.swift; sourceTree = "<group>"; };
		2825A4002DFABE3900EE425C /* RedBagBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RedBagBaseModel.swift; sourceTree = "<group>"; };
		282804DA2D880C540015FA35 /* EditTagSeletedSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTagSeletedSheetView.swift; sourceTree = "<group>"; };
		282804DC2D8817140015FA35 /* TagBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagBaseModel.swift; sourceTree = "<group>"; };
		282804DF2D8832150015FA35 /* TagBaseMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagBaseMainVC.swift; sourceTree = "<group>"; };
		282804E12D8832440015FA35 /* TagOptionListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagOptionListVC.swift; sourceTree = "<group>"; };
		282805462D8984620015FA35 /* EditAdressSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditAdressSheetView.swift; sourceTree = "<group>"; };
		2828054B2D8994C70015FA35 /* UserPageMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPageMainVC.swift; sourceTree = "<group>"; };
		2828054D2D8995220015FA35 /* UserPageHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPageHeaderView.swift; sourceTree = "<group>"; };
		2828054F2D8998920015FA35 /* UserCardListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserCardListVC.swift; sourceTree = "<group>"; };
		282805952D8A63EF0015FA35 /* BasePageInfoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BasePageInfoView.swift; sourceTree = "<group>"; };
		282805AD2D8A99130015FA35 /* UserCardInfoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserCardInfoView.swift; sourceTree = "<group>"; };
		282805AF2D8A9B220015FA35 /* UserCardPhotoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserCardPhotoView.swift; sourceTree = "<group>"; };
		282805B12D8AA1460015FA35 /* UserTagCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserTagCardView.swift; sourceTree = "<group>"; };
		282805B32D8AD4BC0015FA35 /* UserPageBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPageBottomView.swift; sourceTree = "<group>"; };
		2828064B2D8C12430015FA35 /* PearlsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PearlsModel.swift; sourceTree = "<group>"; };
		2828064E2D8C12570015FA35 /* PearlsListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PearlsListVC.swift; sourceTree = "<group>"; };
		282806512D90F42F0015FA35 /* PearlsListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PearlsListCell.swift; sourceTree = "<group>"; };
		282806542D91319F0015FA35 /* RechargeCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RechargeCardView.swift; sourceTree = "<group>"; };
		282806562D9136D60015FA35 /* UserRechargeModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserRechargeModel.swift; sourceTree = "<group>"; };
		282806582D9138910015FA35 /* UserLevelInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserLevelInfoModel.swift; sourceTree = "<group>"; };
		2828066B2D926B630015FA35 /* RecordingPanelController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingPanelController.swift; sourceTree = "<group>"; };
		282806AD2D92A1670015FA35 /* LwChatListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwChatListVC.swift; sourceTree = "<group>"; };
		282806B02D92A3A10015FA35 /* LwChatUserCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwChatUserCell.swift; sourceTree = "<group>"; };
		282806B32D92A3EE0015FA35 /* LwSessionModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwSessionModel.swift; sourceTree = "<group>"; };
		282806B62D92B1670015FA35 /* LwChatDetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwChatDetailVC.swift; sourceTree = "<group>"; };
		282806B82D92B29C0015FA35 /* MessageHandlerProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageHandlerProtocol.swift; sourceTree = "<group>"; };
		282806BB2D92B8C00015FA35 /* MsgBaseCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MsgBaseCell.swift; sourceTree = "<group>"; };
		282806BD2D92B8D30015FA35 /* TextMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextMsgCell.swift; sourceTree = "<group>"; };
		282806BF2D92B8F60015FA35 /* ImageMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageMsgCell.swift; sourceTree = "<group>"; };
		282806C12D92B92B0015FA35 /* NoticeMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoticeMsgCell.swift; sourceTree = "<group>"; };
		282806C42D92B9D80015FA35 /* MsgBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MsgBaseModel.swift; sourceTree = "<group>"; };
		282806C62D92D4F10015FA35 /* GiftMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftMsgCell.swift; sourceTree = "<group>"; };
		282806C92D92D77F0015FA35 /* GiftModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftModel.swift; sourceTree = "<group>"; };
		282806CD2D93D1580015FA35 /* ChatCardCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatCardCell.swift; sourceTree = "<group>"; };
		282F07002D9E873D000EDE0F /* UserPhoneAuthVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPhoneAuthVC.swift; sourceTree = "<group>"; };
		282F07022DA36F7B000EDE0F /* ReportViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReportViewController.swift; sourceTree = "<group>"; };
		282F07052DA3B86B000EDE0F /* InteractListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InteractListVC.swift; sourceTree = "<group>"; };
		282F07072DA4B45B000EDE0F /* UserDynamicCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDynamicCell.swift; sourceTree = "<group>"; };
		282F07092DA4B587000EDE0F /* UserListDynamicVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserListDynamicVC.swift; sourceTree = "<group>"; };
		282F070B2DA60C05000EDE0F /* ChatWarningView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatWarningView.swift; sourceTree = "<group>"; };
		282F070D2DA61022000EDE0F /* FriendRemarkVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FriendRemarkVC.swift; sourceTree = "<group>"; };
		282F070F2DA6206A000EDE0F /* IntimacyTipPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntimacyTipPopView.swift; sourceTree = "<group>"; };
		282F07122DA67767000EDE0F /* ChatHelp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatHelp.swift; sourceTree = "<group>"; };
		283260022D9A3A0B007465A1 /* LwChatMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwChatMainVC.swift; sourceTree = "<group>"; };
		283260062D9A6EC3007465A1 /* LwFriendsMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwFriendsMainVC.swift; sourceTree = "<group>"; };
		283260092D9A6ED3007465A1 /* LwFriendsListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwFriendsListVC.swift; sourceTree = "<group>"; };
		2832600B2D9A6EE8007465A1 /* FriendBaseCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FriendBaseCell.swift; sourceTree = "<group>"; };
		2832600E2D9A79E8007465A1 /* FriendModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FriendModel.swift; sourceTree = "<group>"; };
		283260112D9A7E8E007465A1 /* numFont.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = numFont.ttf; sourceTree = "<group>"; };
		283260132D9A97C4007465A1 /* HobbyListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HobbyListVC.swift; sourceTree = "<group>"; };
		283260152D9A9932007465A1 /* HobbyUserCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HobbyUserCell.swift; sourceTree = "<group>"; };
		283260172D9A9A48007465A1 /* HobbyModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HobbyModel.swift; sourceTree = "<group>"; };
		283260192D9A9FDC007465A1 /* UserGiftListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserGiftListVC.swift; sourceTree = "<group>"; };
		2832601B2D9AA206007465A1 /* NotificationPromptView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationPromptView.swift; sourceTree = "<group>"; };
		2832601D2D9AA3BB007465A1 /* SyteamCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyteamCell.swift; sourceTree = "<group>"; };
		2832601F2D9AA3EE007465A1 /* SyteamModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyteamModel.swift; sourceTree = "<group>"; };
		283263DB2D9B8425007465A1 /* UserFeatureConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserFeatureConfig.swift; sourceTree = "<group>"; };
		283263DD2D9B85AA007465A1 /* UserGiftListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserGiftListCell.swift; sourceTree = "<group>"; };
		283263DF2D9B98B5007465A1 /* ChatStatusPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatStatusPickerView.swift; sourceTree = "<group>"; };
		283263E12D9BBF37007465A1 /* PopoverMenuViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PopoverMenuViewController.swift; sourceTree = "<group>"; };
		283263E32D9BC3C9007465A1 /* AddFreiendListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddFreiendListVC.swift; sourceTree = "<group>"; };
		283263E52D9BE285007465A1 /* LwBaseWebVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LwBaseWebVC.swift; sourceTree = "<group>"; };
		283263E72D9BE704007465A1 /* WebURL.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebURL.swift; sourceTree = "<group>"; };
		283263EA2D9BFBB9007465A1 /* AccountPhoneVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountPhoneVC.swift; sourceTree = "<group>"; };
		283263EC2D9C02B9007465A1 /* VerifyOldPhoneVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerifyOldPhoneVC.swift; sourceTree = "<group>"; };
		283263EE2D9C08FB007465A1 /* VerifyNewPhoneVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerifyNewPhoneVC.swift; sourceTree = "<group>"; };
		283263F12D9C0F98007465A1 /* RealNameAuthVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RealNameAuthVC.swift; sourceTree = "<group>"; };
		283263F32D9C0FD5007465A1 /* RealNameAuthSuccessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RealNameAuthSuccessView.swift; sourceTree = "<group>"; };
		283263F52D9C1A0F007465A1 /* PrivacySecurityVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PrivacySecurityVC.swift; sourceTree = "<group>"; };
		283263F72D9C1A51007465A1 /* SystemPermissionVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemPermissionVC.swift; sourceTree = "<group>"; };
		283263FA2D9C1BB5007465A1 /* PermissionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionManager.swift; sourceTree = "<group>"; };
		283263FC2D9C1DE0007465A1 /* PermissionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionCell.swift; sourceTree = "<group>"; };
		283263FE2D9CC544007465A1 /* NotificationSettingVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationSettingVC.swift; sourceTree = "<group>"; };
		283264002D9CE0FA007465A1 /* DeleteAccountAgreementVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteAccountAgreementVC.swift; sourceTree = "<group>"; };
		283264022D9CEAFC007465A1 /* DeleteAccountCheckModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteAccountCheckModel.swift; sourceTree = "<group>"; };
		283264052D9CEB21007465A1 /* DeleteAccountCheckVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteAccountCheckVC.swift; sourceTree = "<group>"; };
		283264072D9CED23007465A1 /* DeleteAccountConfirmVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteAccountConfirmVC.swift; sourceTree = "<group>"; };
		2832640B2D9D1771007465A1 /* AboutUsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsVC.swift; sourceTree = "<group>"; };
		2832640E2D9D50F7007465A1 /* VoiceCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoiceCell.swift; sourceTree = "<group>"; };
		2832645F2D9E22AA007465A1 /* GiftCollectionVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftCollectionVC.swift; sourceTree = "<group>"; };
		283264622D9E22CE007465A1 /* GiftBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftBaseModel.swift; sourceTree = "<group>"; };
		283264652D9E2459007465A1 /* GiftItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftItemCell.swift; sourceTree = "<group>"; };
		283264672D9E25A5007465A1 /* GiftPanelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftPanelView.swift; sourceTree = "<group>"; };
		283264692D9E26D1007465A1 /* GiftPanelFooterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftPanelFooterView.swift; sourceTree = "<group>"; };
		2832646B2D9E2848007465A1 /* GiftQuantitySelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftQuantitySelectionView.swift; sourceTree = "<group>"; };
		283AB1902DACE8B50086DDB2 /* PlayerManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayerManager.swift; sourceTree = "<group>"; };
		283AB1922DACEEBF0086DDB2 /* AnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnimationPlayer.swift; sourceTree = "<group>"; };
		283AB1942DACEF260086DDB2 /* PNGAnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PNGAnimationPlayer.swift; sourceTree = "<group>"; };
		283AB1962DACF2700086DDB2 /* SVGAAnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SVGAAnimationPlayer.swift; sourceTree = "<group>"; };
		283AB1982DACFCDE0086DDB2 /* LottieAnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LottieAnimationPlayer.swift; sourceTree = "<group>"; };
		283AB19B2DACFF680086DDB2 /* LSZipLottieFileManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LSZipLottieFileManager.swift; sourceTree = "<group>"; };
		283AB19D2DAD031C0086DDB2 /* VapAnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VapAnimationPlayer.swift; sourceTree = "<group>"; };
		283AB19F2DAD048F0086DDB2 /* MP4AnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MP4AnimationPlayer.swift; sourceTree = "<group>"; };
		283AB1A12DAD09500086DDB2 /* FileManagerWrapper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileManagerWrapper.swift; sourceTree = "<group>"; };
		283AB1A32DAD0B190086DDB2 /* AnimationPlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnimationPlayView.swift; sourceTree = "<group>"; };
		283AB3492DB0D1290086DDB2 /* HomeRoomListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeRoomListVC.swift; sourceTree = "<group>"; };
		283AB34B2DB0D1890086DDB2 /* HomeRoomBaseVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeRoomBaseVC.swift; sourceTree = "<group>"; };
		283AB34E2DB0D5380086DDB2 /* RoomManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomManger.swift; sourceTree = "<group>"; };
		283AB3512DB0D9360086DDB2 /* RoomInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomInfoModel.swift; sourceTree = "<group>"; };
		283AB3532DB0DAAA0086DDB2 /* RoomBigCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomBigCell.swift; sourceTree = "<group>"; };
		283AB3542DB0DAAA0086DDB2 /* RoomSmallCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSmallCell.swift; sourceTree = "<group>"; };
		283D57032DF27D80001C4D67 /* YINDONGDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = YINDONGDebug.entitlements; sourceTree = "<group>"; };
		283D57052DF28DA6001C4D67 /* login_new.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; path = login_new.zip; sourceTree = "<group>"; };
		2840B2852D828629007C907C /* GenericActionSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenericActionSheetView.swift; sourceTree = "<group>"; };
		2840EDA12D82E68D000A7EA5 /* DynamicNoticeVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicNoticeVC.swift; sourceTree = "<group>"; };
		2840EDA32D82F248000A7EA5 /* DynoticeModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynoticeModel.swift; sourceTree = "<group>"; };
		2840EDA52D82F32A000A7EA5 /* DyNoticeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DyNoticeCell.swift; sourceTree = "<group>"; };
		2840EDEB2D83C8FC000A7EA5 /* EditMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditMainVC.swift; sourceTree = "<group>"; };
		2840EDF12D83CF10000A7EA5 /* UserInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoModel.swift; sourceTree = "<group>"; };
		2840EDF32D840A6A000A7EA5 /* EditProfileModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileModel.swift; sourceTree = "<group>"; };
		2840EDF62D840A77000A7EA5 /* BaseEditProfileCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseEditProfileCell.swift; sourceTree = "<group>"; };
		2840EDF92D840B54000A7EA5 /* EditPhotoCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditPhotoCell.swift; sourceTree = "<group>"; };
		2840EDFB2D840B7C000A7EA5 /* EditTextInfoCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTextInfoCell.swift; sourceTree = "<group>"; };
		2840EDFD2D840BAA000A7EA5 /* EditUserAlbumCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditUserAlbumCell.swift; sourceTree = "<group>"; };
		2840EDFF2D840BC6000A7EA5 /* EditTagListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTagListCell.swift; sourceTree = "<group>"; };
		2840EE012D841CDD000A7EA5 /* EditProfileDataProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileDataProvider.swift; sourceTree = "<group>"; };
		2840EE032D8431BD000A7EA5 /* EditUserTextSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditUserTextSheetView.swift; sourceTree = "<group>"; };
		2849F88E2DD31AD300FA9D53 /* FloatingScreenView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FloatingScreenView.swift; sourceTree = "<group>"; };
		2849F8902DD31CD000FA9D53 /* BossChestStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossChestStatusView.swift; sourceTree = "<group>"; };
		2849F8942DD3539600FA9D53 /* BossAttackManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossAttackManager.swift; sourceTree = "<group>"; };
		2849F8972DD3718700FA9D53 /* SkillMainListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillMainListVC.swift; sourceTree = "<group>"; };
		2849F8992DD3719700FA9D53 /* SkillListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillListVC.swift; sourceTree = "<group>"; };
		2849F89C2DD4392400FA9D53 /* LipPowerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LipPowerView.swift; sourceTree = "<group>"; };
		2849F89E2DD43A9000FA9D53 /* UpgradeCostView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpgradeCostView.swift; sourceTree = "<group>"; };
		2849F8A02DD43EAE00FA9D53 /* PointsExchangeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsExchangeView.swift; sourceTree = "<group>"; };
		2849F8A32DD46E9D00FA9D53 /* SkillItemModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillItemModel.swift; sourceTree = "<group>"; };
		2849F8A52DD48F3900FA9D53 /* RoomHtmlMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomHtmlMsgCell.swift; sourceTree = "<group>"; };
		2849F8A72DD597CB00FA9D53 /* SkillLevelRatingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillLevelRatingView.swift; sourceTree = "<group>"; };
		2849F8A92DD5BD1A00FA9D53 /* LottieAnimationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LottieAnimationManager.swift; sourceTree = "<group>"; };
		2849F8AF2DD5E0EC00FA9D53 /* LuZiPaySheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LuZiPaySheetView.swift; sourceTree = "<group>"; };
		2849F8B12DD5E20300FA9D53 /* UserLevelMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserLevelMainVC.swift; sourceTree = "<group>"; };
		2849F8B32DD5F1E700FA9D53 /* UserYuanbaoVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserYuanbaoVC.swift; sourceTree = "<group>"; };
		28553F4F2D87C3710042C619 /* CommonAlignFlowLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonAlignFlowLayout.swift; sourceTree = "<group>"; };
		285DA6472DFAC1EB00BDCDAD /* RedActView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RedActView.swift; sourceTree = "<group>"; };
		285DA6492DFACFF500BDCDAD /* BossRedRobView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossRedRobView.swift; sourceTree = "<group>"; };
		285DA64B2DFAF02900BDCDAD /* BossRedRedBegainV.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossRedRedBegainV.swift; sourceTree = "<group>"; };
		285DA64D2DFAFD9600BDCDAD /* RedLuckUserPopV.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RedLuckUserPopV.swift; sourceTree = "<group>"; };
		285DA6502DFBFF2900BDCDAD /* UserPkSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPkSheetView.swift; sourceTree = "<group>"; };
		285DA6532DFC1D1A00BDCDAD /* SkillInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillInfoModel.swift; sourceTree = "<group>"; };
		2866EB7F2DC1D743005B0D13 /* RoomUserMainSheetV.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomUserMainSheetV.swift; sourceTree = "<group>"; };
		2866EB812DC1D75B005B0D13 /* OnlineListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnlineListVC.swift; sourceTree = "<group>"; };
		2866EB832DC1D766005B0D13 /* RankListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RankListVC.swift; sourceTree = "<group>"; };
		28785FE52E0A7D9900367787 /* UserInfoCollectionModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoCollectionModel.swift; sourceTree = "<group>"; };
		28785FE82E0A8B4B00367787 /* MainGoodsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainGoodsVC.swift; sourceTree = "<group>"; };
		28785FEB2E0A91FB00367787 /* DressUpListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DressUpListCell.swift; sourceTree = "<group>"; };
		28785FEE2E0A970800367787 /* DressBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DressBaseModel.swift; sourceTree = "<group>"; };
		28785FF02E0A9A1600367787 /* DecorationMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DecorationMainVC.swift; sourceTree = "<group>"; };
		28785FF22E0A9A2800367787 /* DecorationListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DecorationListVC.swift; sourceTree = "<group>"; };
		28785FF42E0AAFA200367787 /* RingListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RingListVC.swift; sourceTree = "<group>"; };
		28785FF62E0ABC5C00367787 /* HotGoodsListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HotGoodsListVC.swift; sourceTree = "<group>"; };
		28785FF82E0AC85F00367787 /* DressBuyPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DressBuyPopView.swift; sourceTree = "<group>"; };
		28785FFA2E0B887200367787 /* ShowSpecialView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShowSpecialView.swift; sourceTree = "<group>"; };
		28785FFC2E0BA49400367787 /* BaseChatBgView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseChatBgView.swift; sourceTree = "<group>"; };
		28785FFE2E0BDB5300367787 /* EventPreviewShowView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EventPreviewShowView.swift; sourceTree = "<group>"; };
		287860002E0BEBA200367787 /* PurchaseSuccessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseSuccessView.swift; sourceTree = "<group>"; };
		287860032E0BF63700367787 /* MyPackMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyPackMainVC.swift; sourceTree = "<group>"; };
		287860052E0BF64F00367787 /* MyPackListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyPackListVC.swift; sourceTree = "<group>"; };
		287860072E0C0F8E00367787 /* WearPrePopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WearPrePopView.swift; sourceTree = "<group>"; };
		287860092E0C1D7B00367787 /* RingBuyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RingBuyView.swift; sourceTree = "<group>"; };
		2878600B2E0CF46F00367787 /* APIHistorySheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIHistorySheetView.swift; sourceTree = "<group>"; };
		287E93532DDEB655004EEDFB /* RoomNoticeEditVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomNoticeEditVC.swift; sourceTree = "<group>"; };
		287E93552DDEF71D004EEDFB /* RoomAdminListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomAdminListVC.swift; sourceTree = "<group>"; };
		287E93572DDEF728004EEDFB /* RoomAdminAddVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomAdminAddVC.swift; sourceTree = "<group>"; };
		287E93F82DE06F1A004EEDFB /* GiftQueManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftQueManager.swift; sourceTree = "<group>"; };
		287E93FA2DE06F4A004EEDFB /* QueGift.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QueGift.swift; sourceTree = "<group>"; };
		287E93FC2DE06F78004EEDFB /* GiftShowView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftShowView.swift; sourceTree = "<group>"; };
		287E93FE2DE06FA8004EEDFB /* GiftNumView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftNumView.swift; sourceTree = "<group>"; };
		287E94002DE3FF0D004EEDFB /* RoomBgListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomBgListVC.swift; sourceTree = "<group>"; };
		287E94022DE43C9E004EEDFB /* RoomGiftOpenSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomGiftOpenSheetView.swift; sourceTree = "<group>"; };
		287E94052DE45DA2004EEDFB /* RoomSongMainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSongMainView.swift; sourceTree = "<group>"; };
		287E94072DE463B8004EEDFB /* RoomSongListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSongListVC.swift; sourceTree = "<group>"; };
		287E940A2DE46823004EEDFB /* MusicPlaybackManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicPlaybackManager.swift; sourceTree = "<group>"; };
		287E940C2DE4722D004EEDFB /* MusicPlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicPlayView.swift; sourceTree = "<group>"; };
		287E940E2DE47358004EEDFB /* RoomSongCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSongCell.swift; sourceTree = "<group>"; };
		287E94102DE54DE2004EEDFB /* RoomInviteMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomInviteMsgCell.swift; sourceTree = "<group>"; };
		287E94122DE55F79004EEDFB /* RoomItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomItemView.swift; sourceTree = "<group>"; };
		287E94142DE56DD7004EEDFB /* CpListSyVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CpListSyVC.swift; sourceTree = "<group>"; };
		287E94162DE5931D004EEDFB /* CpManeuverCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CpManeuverCell.swift; sourceTree = "<group>"; };
		************************ /* InvterRoomCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvterRoomCell.swift; sourceTree = "<group>"; };
		287E941A2DE5B20D004EEDFB /* SyTextLinkCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyTextLinkCell.swift; sourceTree = "<group>"; };
		287E941C2DE5BCA7004EEDFB /* InvterFansPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvterFansPopView.swift; sourceTree = "<group>"; };
		287E948B2DE5D717004EEDFB /* RoomInvterUserSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomInvterUserSheetView.swift; sourceTree = "<group>"; };
		287E948D2DE6B123004EEDFB /* RoomLockPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomLockPopView.swift; sourceTree = "<group>"; };
		287E948F2DE6F0D6004EEDFB /* RoomSettingSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSettingSheetView.swift; sourceTree = "<group>"; };
		287E94912DE70195004EEDFB /* RoomChatInputPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomChatInputPopupView.swift; sourceTree = "<group>"; };
		287E94932DE7F04F004EEDFB /* UserDisguiseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDisguiseModel.swift; sourceTree = "<group>"; };
		287E94952DE7FA4E004EEDFB /* UserTitleModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserTitleModel.swift; sourceTree = "<group>"; };
		287E94982DE8423F004EEDFB /* GiftUserListBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GiftUserListBar.swift; sourceTree = "<group>"; };
		287E949A2DE87AEA004EEDFB /* BossTiaoJIanSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossTiaoJIanSheetView.swift; sourceTree = "<group>"; };
		288548E52D6F3F200047A69D /* PassWordAccountVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PassWordAccountVC.swift; sourceTree = "<group>"; };
		288548E82D6FF8260047A69D /* T_IMHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = T_IMHelper.swift; sourceTree = "<group>"; };
		288548EA2D7002530047A69D /* LWUserModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LWUserModel.swift; sourceTree = "<group>"; };
		288548EC2D7002F30047A69D /* LWUserManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LWUserManger.swift; sourceTree = "<group>"; };
		288548EE2D70065A0047A69D /* WaitLuanchVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WaitLuanchVC.swift; sourceTree = "<group>"; };
		288548F02D7019EF0047A69D /* PasswordLoginVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PasswordLoginVC.swift; sourceTree = "<group>"; };
		288548F22D7073D80047A69D /* ThirdBindPhoneVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThirdBindPhoneVC.swift; sourceTree = "<group>"; };
		288548F52D718CE70047A69D /* InfoItemBaseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InfoItemBaseView.swift; sourceTree = "<group>"; };
		288548F72D719D9E0047A69D /* BaseBannerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseBannerView.swift; sourceTree = "<group>"; };
		288548FA2D71A2DB0047A69D /* DyHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DyHeaderView.swift; sourceTree = "<group>"; };
		288548FD2D71A5F30047A69D /* BannerModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BannerModel.swift; sourceTree = "<group>"; };
		288548FF2D71B8EB0047A69D /* TopicCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicCell.swift; sourceTree = "<group>"; };
		288549022D71B9B50047A69D /* TopicModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicModel.swift; sourceTree = "<group>"; };
		288549042D753BFB0047A69D /* ExpandPushAnimator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpandPushAnimator.swift; sourceTree = "<group>"; };
		288549092D7541280047A69D /* TopicDetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicDetailVC.swift; sourceTree = "<group>"; };
		2885490B2D75522C0047A69D /* CreatePostVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreatePostVC.swift; sourceTree = "<group>"; };
		2885490D2D7554B80047A69D /* TopicSelListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicSelListVC.swift; sourceTree = "<group>"; };
		2885490F2D755D8E0047A69D /* ImageExpandingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageExpandingView.swift; sourceTree = "<group>"; };
		288549112D75909D0047A69D /* DynamicBaseCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicBaseCell.swift; sourceTree = "<group>"; };
		288549132D7594A40047A69D /* DynamicBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicBaseModel.swift; sourceTree = "<group>"; };
		288549152D7595150047A69D /* DynamicMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicMainVC.swift; sourceTree = "<group>"; };
		288549172D75954F0047A69D /* DynamicBaseListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicBaseListVC.swift; sourceTree = "<group>"; };
		288549192D7598760047A69D /* ImageCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageCardView.swift; sourceTree = "<group>"; };
		2885491B2D759C750047A69D /* TagLabelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagLabelView.swift; sourceTree = "<group>"; };
		2885491D2D7697480047A69D /* DynamicDetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicDetailVC.swift; sourceTree = "<group>"; };
		2885491F2D7698BF0047A69D /* DynamicDetailHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicDetailHeaderView.swift; sourceTree = "<group>"; };
		288549212D76D9BC0047A69D /* RemarkTopCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemarkTopCell.swift; sourceTree = "<group>"; };
		288549232D76E04B0047A69D /* CommentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentModel.swift; sourceTree = "<group>"; };
		288549252D76E0F10047A69D /* CommentOneCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentOneCell.swift; sourceTree = "<group>"; };
		288549272D76FA020047A69D /* String_Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = String_Ext.swift; sourceTree = "<group>"; };
		2885492C2D77248C0047A69D /* LocationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocationManager.swift; sourceTree = "<group>"; };
		288549302D7729A40047A69D /* le_city.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = le_city.json; sourceTree = "<group>"; };
		288549322D77EB330047A69D /* DynamicFollowVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicFollowVC.swift; sourceTree = "<group>"; };
		288549342D77EE780047A69D /* FollowNodataView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowNodataView.swift; sourceTree = "<group>"; };
		288549362D77FAC20047A69D /* DynamicNewListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicNewListVC.swift; sourceTree = "<group>"; };
		288549382D77FB380047A69D /* DynamicLocListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicLocListVC.swift; sourceTree = "<group>"; };
		2885493A2D7829180047A69D /* loading2.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = loading2.json; sourceTree = "<group>"; };
		2885493C2D782AF80047A69D /* LWLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LWLoadingView.swift; sourceTree = "<group>"; };
		2885493E2D782C520047A69D /* LodingRefeshHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LodingRefeshHeaderView.swift; sourceTree = "<group>"; };
		288549402D783A390047A69D /* VoiceBaseCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoiceBaseCardView.swift; sourceTree = "<group>"; };
		288549422D783DD80047A69D /* SVGAnimationPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SVGAnimationPlayer.swift; sourceTree = "<group>"; };
		288549442D7843330047A69D /* dy_voicePlay.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = dy_voicePlay.svga; sourceTree = "<group>"; };
		288549472D7846FD0047A69D /* MediaPlaybackManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MediaPlaybackManager.swift; sourceTree = "<group>"; };
		2885494A2D787A490047A69D /* DyInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DyInputView.swift; sourceTree = "<group>"; };
		2885494C2D7937E30047A69D /* emoji_mapping.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = emoji_mapping.json; sourceTree = "<group>"; };
		2885494F2D7938300047A69D /* EmojiModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmojiModel.swift; sourceTree = "<group>"; };
		288549512D7938530047A69D /* EmojiManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmojiManger.swift; sourceTree = "<group>"; };
		288549532D793C370047A69D /* EmojiKeyboardVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmojiKeyboardVC.swift; sourceTree = "<group>"; };
		288549552D793E0E0047A69D /* EmojiMainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmojiMainView.swift; sourceTree = "<group>"; };
		2885499D2D79BB6D0047A69D /* EmojiTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmojiTextView.swift; sourceTree = "<group>"; };
		289F9E032DC9F7D300D7522D /* RoomToolSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomToolSheetView.swift; sourceTree = "<group>"; };
		289F9E392DCA027D00D7522D /* VoiceRoomActivityCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoiceRoomActivityCell.swift; sourceTree = "<group>"; };
		289F9E3B2DCA02BD00D7522D /* VoiceRoomActivitySheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoiceRoomActivitySheetView.swift; sourceTree = "<group>"; };
		289F9E882DCB0C9000D7522D /* BossAttackFloatingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossAttackFloatingView.swift; sourceTree = "<group>"; };
		289F9E8B2DCB0D7D00D7522D /* boss_max_aida.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_max_aida.svga; sourceTree = "<group>"; };
		289F9E8C2DCB0D7D00D7522D /* boss_max_xh.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_max_xh.svga; sourceTree = "<group>"; };
		289F9E8D2DCB0D7D00D7522D /* boss_mini_aida.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_mini_aida.svga; sourceTree = "<group>"; };
		289F9E8E2DCB0D7D00D7522D /* boss_mini_xh.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_mini_xh.svga; sourceTree = "<group>"; };
		289F9E932DCC4E7000D7522D /* BoosMainSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BoosMainSheetView.swift; sourceTree = "<group>"; };
		289F9E962DCC59F000D7522D /* BossRecordTickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossRecordTickerView.swift; sourceTree = "<group>"; };
		289F9E992DCC5A4100D7522D /* BossRecordRotation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossRecordRotation.swift; sourceTree = "<group>"; };
		289F9E9B2DCC5C6400D7522D /* boss_fazhen.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_fazhen.svga; sourceTree = "<group>"; };
		289F9E9D2DCC929200D7522D /* gaojiMofa.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = gaojiMofa.mp4; sourceTree = "<group>"; };
		289F9E9F2DCC98E100D7522D /* CustomProgressBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomProgressBar.swift; sourceTree = "<group>"; };
		289F9EA52DCC9F4200D7522D /* boss_box.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; path = boss_box.zip; sourceTree = "<group>"; };
		289F9EA72DCCAB6600D7522D /* putongMF.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = putongMF.mp4; sourceTree = "<group>"; };
		289F9EA92DCCAC9F00D7522D /* boss_attPath.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_attPath.svga; sourceTree = "<group>"; };
		289F9EAA2DCCAC9F00D7522D /* boss_boom.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = boss_boom.svga; sourceTree = "<group>"; };
		289F9EAD2DCCD3B600D7522D /* BossBuyBookSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossBuyBookSheetView.swift; sourceTree = "<group>"; };
		289F9F682DCDA50F00D7522D /* BossValueView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossValueView.swift; sourceTree = "<group>"; };
		289F9F6A2DCDEE6000D7522D /* StackedNotificationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StackedNotificationView.swift; sourceTree = "<group>"; };
		28AFCD5B2DB120C2003CE291 /* HotRoomMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HotRoomMainVC.swift; sourceTree = "<group>"; };
		28AFCDA92DB5E41B003CE291 /* RoomSnapshot.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSnapshot.swift; sourceTree = "<group>"; };
		28AFCDAC2DB5F740003CE291 /* RoomSeatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSeatView.swift; sourceTree = "<group>"; };
		28AFCDAE2DB5F790003CE291 /* RoomContainerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomContainerView.swift; sourceTree = "<group>"; };
		28AFCDB32DB654C2003CE291 /* SideMenuView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideMenuView.swift; sourceTree = "<group>"; };
		28AFCDB52DB65ABE003CE291 /* RoomInfoTopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomInfoTopView.swift; sourceTree = "<group>"; };
		28AFCDB82DB66717003CE291 /* RoomDetailModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomDetailModel.swift; sourceTree = "<group>"; };
		28AFCDBA2DB72430003CE291 /* AudioWaveView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioWaveView.swift; sourceTree = "<group>"; };
		28AFCDBC2DB73070003CE291 /* CreateRoomVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateRoomVC.swift; sourceTree = "<group>"; };
		28AFCDBE2DB730C3003CE291 /* RoomTypeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomTypeCell.swift; sourceTree = "<group>"; };
		28AFCDC02DB73945003CE291 /* RoomTypeModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomTypeModel.swift; sourceTree = "<group>"; };
		28AFCDC22DB73AA4003CE291 /* GameTypeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameTypeCell.swift; sourceTree = "<group>"; };
		28AFCDC42DB791E5003CE291 /* AuthenticationPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationPopupView.swift; sourceTree = "<group>"; };
		28AFCDFE2DB79CD4003CE291 /* RoomFloatingWindow.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomFloatingWindow.swift; sourceTree = "<group>"; };
		28AFCE002DB7A7BF003CE291 /* RoomFloatingWindowManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomFloatingWindowManager.swift; sourceTree = "<group>"; };
		28AFCE022DB7B776003CE291 /* RoomBgView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomBgView.swift; sourceTree = "<group>"; };
		28AFCE042DB88728003CE291 /* AvatarFrameView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AvatarFrameView.swift; sourceTree = "<group>"; };
		28AFCE062DB8DE71003CE291 /* RoomMsgManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomMsgManger.swift; sourceTree = "<group>"; };
		28AFCE082DB9CA64003CE291 /* TickHub.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TickHub.swift; sourceTree = "<group>"; };
		28AFCE782DBA123A003CE291 /* RoomViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomViewModel.swift; sourceTree = "<group>"; };
		28AFCE7A2DBA127D003CE291 /* RoomPublicMessageVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomPublicMessageVC.swift; sourceTree = "<group>"; };
		28AFCE7C2DBA12A3003CE291 /* RoomBaseMsgModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomBaseMsgModel.swift; sourceTree = "<group>"; };
		28AFCE812DBA2D56003CE291 /* RoomSeatConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSeatConfiguration.swift; sourceTree = "<group>"; };
		28AFCE832DBA4395003CE291 /* RoomMsgType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomMsgType.swift; sourceTree = "<group>"; };
		28AFCE852DBA45A2003CE291 /* RoomMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomMessageCell.swift; sourceTree = "<group>"; };
		28AFCEF32DBB6882003CE291 /* RoomNavUserView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomNavUserView.swift; sourceTree = "<group>"; };
		28AFCF102DBB7297003CE291 /* RoomUserInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomUserInfoModel.swift; sourceTree = "<group>"; };
		28AFCF3B2DBB9353003CE291 /* RoomChatBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomChatBarView.swift; sourceTree = "<group>"; };
		28AFCF462DBDC96E003CE291 /* CircleProgressView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CircleProgressView.swift; sourceTree = "<group>"; };
		28AFCF482DBDCD3A003CE291 /* LuckSproutButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LuckSproutButton.swift; sourceTree = "<group>"; };
		28AFCF972DBE3AA2003CE291 /* RoomInfoCardSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomInfoCardSheetView.swift; sourceTree = "<group>"; };
		28AFCF9C2DBF34CB003CE291 /* AppConfigManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConfigManger.swift; sourceTree = "<group>"; };
		28AFCF9E2DBF5A03003CE291 /* TagListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagListView.swift; sourceTree = "<group>"; };
		28AFCFCB2DBF8B49003CE291 /* SeatActionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeatActionManager.swift; sourceTree = "<group>"; };
		28AFCFDA2DC0CD91003CE291 /* RoomDetailSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomDetailSheetView.swift; sourceTree = "<group>"; };
		28B931C32D7A8F6000DE4F8E /* ChatInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatInputView.swift; sourceTree = "<group>"; };
		28B931C52D7ACD9900DE4F8E /* DyPostInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DyPostInputView.swift; sourceTree = "<group>"; };
		28B931C72D7AF12800DE4F8E /* VoicePostView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoicePostView.swift; sourceTree = "<group>"; };
		28B931C92D7AF93900DE4F8E /* AudioRecordSession.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioRecordSession.swift; sourceTree = "<group>"; };
		28B932332D7ED6F900DE4F8E /* DySelUserlistVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DySelUserlistVC.swift; sourceTree = "<group>"; };
		28B932352D7ED83100DE4F8E /* DySelUserCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DySelUserCell.swift; sourceTree = "<group>"; };
		28B932372D7EFCCA00DE4F8E /* DyImagePickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DyImagePickerView.swift; sourceTree = "<group>"; };
		28B932392D801D8900DE4F8E /* RateLimitManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RateLimitManager.swift; sourceTree = "<group>"; };
		28B932932D81367B00DE4F8E /* LWJumpManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LWJumpManger.swift; sourceTree = "<group>"; };
		28B932D62D81602000DE4F8E /* MessagePopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessagePopupView.swift; sourceTree = "<group>"; };
		28BB749D2DD19D44004B9997 /* BossJpMainSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossJpMainSheetView.swift; sourceTree = "<group>"; };
		28BB749F2DD1CDCF004B9997 /* BossJpListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossJpListVC.swift; sourceTree = "<group>"; };
		28BB74A12DD1D31F004B9997 /* JiangPingCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JiangPingCell.swift; sourceTree = "<group>"; };
		28BB74A32DD1D348004B9997 /* JingLiCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JingLiCell.swift; sourceTree = "<group>"; };
		28BB74A52DD1E925004B9997 /* BossMoreTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossMoreTextView.swift; sourceTree = "<group>"; };
		28BB74A72DD1ED0F004B9997 /* BossGoodsdhSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossGoodsdhSheetView.swift; sourceTree = "<group>"; };
		28BB74A92DD1F7F1004B9997 /* BossGoodsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossGoodsCell.swift; sourceTree = "<group>"; };
		28BB74AB2DD1FDDF004B9997 /* BossMyRateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BossMyRateView.swift; sourceTree = "<group>"; };
		28C1ED0A2DD72CE1006F0442 /* TuPoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TuPoView.swift; sourceTree = "<group>"; };
		28C1ED0C2DD72CF6006F0442 /* UpgradeConditionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpgradeConditionView.swift; sourceTree = "<group>"; };
		28C1ED0F2DD73E68006F0442 /* SkillGoodsMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillGoodsMainVC.swift; sourceTree = "<group>"; };
		28C1ED112DD73E71006F0442 /* SkillGoodsListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SkillGoodsListVC.swift; sourceTree = "<group>"; };
		28C1ED162DDAC203006F0442 /* CurrencyBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrencyBottomView.swift; sourceTree = "<group>"; };
		28C1ED192DDACDF0006F0442 /* GoodsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoodsCell.swift; sourceTree = "<group>"; };
		28C1ED1B2DDADF9E006F0442 /* GoodsBuyPopView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoodsBuyPopView.swift; sourceTree = "<group>"; };
		28C1ED1E2DDB2E1E006F0442 /* RoomTextMsgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomTextMsgCell.swift; sourceTree = "<group>"; };
		28E21A6B2D6C68280007ED12 /* EnvManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnvManager.swift; sourceTree = "<group>"; };
		28E21A6E2D6C687F0007ED12 /* AppTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppTool.swift; sourceTree = "<group>"; };
		28E21A702D6C6AF30007ED12 /* ApiResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApiResponse.swift; sourceTree = "<group>"; };
		28E21A722D6C6C340007ED12 /* AppConfigUI_Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConfigUI_Ext.swift; sourceTree = "<group>"; };
		28E21A742D6C6F530007ED12 /* ApiService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApiService.swift; sourceTree = "<group>"; };
		28E21A762D6C706B0007ED12 /* NetworkUtility.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkUtility.swift; sourceTree = "<group>"; };
		28E21A792D6C7DAC0007ED12 /* SmAntiFraud.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = SmAntiFraud.xcframework; sourceTree = "<group>"; };
		28E21A7B2D6C7DF40007ED12 /* YINDONG-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "YINDONG-Bridging-Header.h"; sourceTree = "<group>"; };
		28E21A7F2D6C7FE30007ED12 /* CoreAudioTypes.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioTypes.framework; path = System/Library/Frameworks/CoreAudioTypes.framework; sourceTree = SDKROOT; };
		28E21A812D6C802B0007ED12 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		28E21A832D6C803D0007ED12 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		28E21A8C2D6D6BAA0007ED12 /* Network.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Network.framework; path = System/Library/Frameworks/Network.framework; sourceTree = SDKROOT; };
		28E21A8E2D6D6BF40007ED12 /* ATAuthSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ATAuthSDK.framework; sourceTree = "<group>"; };
		28E21A8F2D6D6BF40007ED12 /* YTXMonitor.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXMonitor.framework; sourceTree = "<group>"; };
		28E21A902D6D6BF40007ED12 /* YTXOperators.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXOperators.framework; sourceTree = "<group>"; };
		28E21A952D6D6C1B0007ED12 /* QuickManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickManger.swift; sourceTree = "<group>"; };
		28E21A972D6D6C8B0007ED12 /* ATAuthSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; name = ATAuthSDK.bundle; path = YINDONG/Main/Manger/QuickLogin/ATAuthSDK.framework/ATAuthSDK.bundle; sourceTree = "<group>"; };
		28E21A9A2D6D98590007ED12 /* PrivacySettingsSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PrivacySettingsSheetView.swift; sourceTree = "<group>"; };
		28E21A9C2D6D9B550007ED12 /* Notification_Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Notification_Ext.swift; sourceTree = "<group>"; };
		28E21A9E2D6D9E510007ED12 /* ThirdQuickView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThirdQuickView.swift; sourceTree = "<group>"; };
		28E21AA12D6DA0AC0007ED12 /* AliyunOssHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AliyunOssHelper.swift; sourceTree = "<group>"; };
		28E21AA32D6DAC030007ED12 /* ProfileSetupVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileSetupVC.swift; sourceTree = "<group>"; };
		28E21AA62D6DC1470007ED12 /* QuickLoginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickLoginView.swift; sourceTree = "<group>"; };
		28E21AA82D6DD7100007ED12 /* SendCodeVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SendCodeVC.swift; sourceTree = "<group>"; };
		28E21AAB2D6EB1930007ED12 /* LoginUserModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginUserModel.swift; sourceTree = "<group>"; };
		28E21AAD2D6EB5C00007ED12 /* SelectAccountVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SelectAccountVC.swift; sourceTree = "<group>"; };
		28E21AAF2D6EED690007ED12 /* PasswordSettingVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PasswordSettingVC.swift; sourceTree = "<group>"; };
		28E21AB12D6EF1AD0007ED12 /* FeedbackVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedbackVC.swift; sourceTree = "<group>"; };
		28E21AB32D6F05390007ED12 /* YINDONG.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = YINDONG.entitlements; sourceTree = "<group>"; };
		28E21AB42D6F11BB0007ED12 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		28E785302DC9AD440092CED9 /* AudienceRoomClosedVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudienceRoomClosedVC.swift; sourceTree = "<group>"; };
		28E785322DC9BA590092CED9 /* RoomSummaryVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSummaryVC.swift; sourceTree = "<group>"; };
		28EAA6B32D9E7CC0001600E8 /* MatchQuestionModalView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MatchQuestionModalView.swift; sourceTree = "<group>"; };
		28EB87E82DEED09200D389FB /* RoomEditTitleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomEditTitleView.swift; sourceTree = "<group>"; };
		28EB87EA2DEFEB8000D389FB /* RoomTypeSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomTypeSelectionView.swift; sourceTree = "<group>"; };
		28EB87EC2DF0214200D389FB /* RoomSeatDegreeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoomSeatDegreeView.swift; sourceTree = "<group>"; };
		28EB87EE2DF0440000D389FB /* HotInfoPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HotInfoPopupView.swift; sourceTree = "<group>"; };
		28EB87F02DF1383D00D389FB /* VolumePopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VolumePopupView.swift; sourceTree = "<group>"; };
		28F6433E2D93F2BE00487E43 /* ChatDetailNavView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatDetailNavView.swift; sourceTree = "<group>"; };
		28F643402D93F8D200487E43 /* ChatDetailUserModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatDetailUserModel.swift; sourceTree = "<group>"; };
		28F643DC2D9535F700487E43 /* SendMsg-Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SendMsg-Ext.swift"; sourceTree = "<group>"; };
		28F643E82D953B0700487E43 /* AudioRecordingProgressView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioRecordingProgressView.swift; sourceTree = "<group>"; };
		28F643EA2D953B1B00487E43 /* AudioVisualizerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioVisualizerView.swift; sourceTree = "<group>"; };
		28F643EC2D95656D00487E43 /* TacitOptionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TacitOptionCell.swift; sourceTree = "<group>"; };
		28F643EE2D95664800487E43 /* TacitModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TacitModel.swift; sourceTree = "<group>"; };
		28F643F02D967DBC00487E43 /* TacitOptionCardSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TacitOptionCardSheetView.swift; sourceTree = "<group>"; };
		6AFDABB36EC1DFB76A8E2A8B /* Pods-YINDONG.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YINDONG.release.xcconfig"; path = "Target Support Files/Pods-YINDONG/Pods-YINDONG.release.xcconfig"; sourceTree = "<group>"; };
		6F053B6ABFE3B6136BECD374 /* Pods_YINDONG.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_YINDONG.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		80AB014BE5AAB7F6B13EA95F /* Pods-YINDONG.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YINDONG.debug.xcconfig"; path = "Target Support Files/Pods-YINDONG/Pods-YINDONG.debug.xcconfig"; sourceTree = "<group>"; };
		954188172D0558990043071D /* MessageStorage.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageStorage.swift; sourceTree = "<group>"; };
		954188192D055ABF0043071D /* ChatAudioManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChatAudioManager.swift; sourceTree = "<group>"; };
		9541881C2D055B520043071D /* MessageCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageCell.swift; sourceTree = "<group>"; };
		9541881E2D0561F90043071D /* ChatUserManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChatUserManager.swift; sourceTree = "<group>"; };
		9541881F2D0561F90043071D /* BlacklistManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BlacklistManager.swift; sourceTree = "<group>"; };
		954188222D05762F0043071D /* MicrophonePositionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MicrophonePositionView.swift; sourceTree = "<group>"; };
		954188232D05762F0043071D /* PublicMessageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PublicMessageView.swift; sourceTree = "<group>"; };
		954188262D0579200043071D /* WebViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebViewController.swift; sourceTree = "<group>"; };
		9567954B2CFF528C00F03EF8 /* ChatListVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChatListVC.swift; sourceTree = "<group>"; };
		9567954C2CFF528C00F03EF8 /* ChatDetailVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChatDetailVC.swift; sourceTree = "<group>"; };
		9567954F2CFF529300F03EF8 /* ChatListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChatListCell.swift; sourceTree = "<group>"; };
		956795522CFF529300F03EF8 /* ConversationModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConversationModel.swift; sourceTree = "<group>"; };
		956795532CFF529300F03EF8 /* MessageModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageModel.swift; sourceTree = "<group>"; };
		956795592CFF711100F03EF8 /* LoginVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginVC.swift; sourceTree = "<group>"; };
		9567955E2CFF711F00F03EF8 /* UserModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserModel.swift; sourceTree = "<group>"; };
		956795612CFF72D000F03EF8 /* BlacklistVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BlacklistVC.swift; sourceTree = "<group>"; };
		956795622CFF72D000F03EF8 /* SettingsVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingsVC.swift; sourceTree = "<group>"; };
		959B2ACA2CE840A90047E096 /* YINDONG.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = YINDONG.app; sourceTree = BUILT_PRODUCTS_DIR; };
		959B2ACD2CE840A90047E096 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		959B2AD62CE840AA0047E096 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		959B2AD92CE840AA0047E096 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		959B2ADB2CE840AA0047E096 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		959B2AE32CE843010047E096 /* MainTabBarVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabBarVC.swift; sourceTree = "<group>"; };
		959B2AE52CE844420047E096 /* YinDBaseVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YinDBaseVC.swift; sourceTree = "<group>"; };
		959B2AEF2CE84AFD0047E096 /* UniversalPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UniversalPopupView.swift; sourceTree = "<group>"; };
		959B2AF72CE84D6D0047E096 /* MusicTheoryQuizViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicTheoryQuizViewController.swift; sourceTree = "<group>"; };
		959B2AF92CE84E760047E096 /* InstrumentCollectionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InstrumentCollectionViewController.swift; sourceTree = "<group>"; };
		959B2AFC2CE851890047E096 /* TCApAudioRecordingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TCApAudioRecordingManager.swift; sourceTree = "<group>"; };
		959B2AFF2CE8520C0047E096 /* AudioRecordingHUDView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioRecordingHUDView.swift; sourceTree = "<group>"; };
		959B2B022CE853440047E096 /* RecordingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingViewController.swift; sourceTree = "<group>"; };
		959B2B042CE85D120047E096 /* ChatMainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatMainVC.swift; sourceTree = "<group>"; };
		959B2B062CE85D960047E096 /* SyteamListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyteamListVC.swift; sourceTree = "<group>"; };
		959B2B092CE862770047E096 /* MineVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MineVC.swift; sourceTree = "<group>"; };
		959B2B0B2CE880250047E096 /* RecordingTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingTableViewCell.swift; sourceTree = "<group>"; };
		959B2B0D2CE8803C0047E096 /* RecordingsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingsViewController.swift; sourceTree = "<group>"; };
		959B2B162CE8B4320047E096 /* MusicEvent.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MusicEvent.swift; sourceTree = "<group>"; };
		959B2B182CE8B44F0047E096 /* ActivityTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ActivityTableViewCell.swift; sourceTree = "<group>"; };
		959B2B1A2CE8B4630047E096 /* MusicActListVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MusicActListVC.swift; sourceTree = "<group>"; };
		959B2B1C2CE8B4740047E096 /* MusicEventPopView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MusicEventPopView.swift; sourceTree = "<group>"; };
		95DAA59C2D2D6CA500126D4E /* PostModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PostModel.swift; sourceTree = "<group>"; };
		95DAA59E2D2D6CAE00126D4E /* PostCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PostCell.swift; sourceTree = "<group>"; };
		95DAA5A02D2D6CAE00126D4E /* SocialFeedViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocialFeedViewController.swift; sourceTree = "<group>"; };
		95DAA5A42D2D6CCF00126D4E /* TwoMainViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TwoMainViewController.swift; sourceTree = "<group>"; };
		95DAA5A62D2D70EA00126D4E /* CreatePostViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreatePostViewController.swift; sourceTree = "<group>"; };
		95DAA5A72D2D70EA00126D4E /* PostDetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PostDetailViewController.swift; sourceTree = "<group>"; };
		95DAA5AA2D2D730400126D4E /* MusicianListViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MusicianListViewController.swift; sourceTree = "<group>"; };
		95DAA5AC2D2D730A00126D4E /* FeedListViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FeedListViewController.swift; sourceTree = "<group>"; };
		95DAA5AE2D2D731200126D4E /* MusicianCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MusicianCell.swift; sourceTree = "<group>"; };
		95DAA5B02D2D732300126D4E /* MusicianModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MusicianModel.swift; sourceTree = "<group>"; };
		95DAA5B22D2D750D00126D4E /* UserProfileViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserProfileViewController.swift; sourceTree = "<group>"; };
		95DAA5B42D2D789600126D4E /* ReportModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReportModel.swift; sourceTree = "<group>"; };
		95DAA5B62D2D78A500126D4E /* ReportActionSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReportActionSheet.swift; sourceTree = "<group>"; };
		95DAA5B82D2D79E100126D4E /* BlockedUserManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BlockedUserManager.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		959B2AC72CE840A90047E096 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28E21A8D2D6D6BAA0007ED12 /* Network.framework in Frameworks */,
				28E21A842D6C803D0007ED12 /* CoreLocation.framework in Frameworks */,
				28E21A822D6C802B0007ED12 /* IOKit.framework in Frameworks */,
				28E21A912D6D6BF40007ED12 /* YTXOperators.framework in Frameworks */,
				28E21A922D6D6BF40007ED12 /* ATAuthSDK.framework in Frameworks */,
				28E21A932D6D6BF40007ED12 /* YTXMonitor.framework in Frameworks */,
				28E21A7A2D6C7DAC0007ED12 /* SmAntiFraud.xcframework in Frameworks */,
				421847C4AAFF6E1248DC2A86 /* Pods_YINDONG.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		281B40472E018FA600939041 /* skill_level */ = {
			isa = PBXGroup;
			children = (
				281B40482E018FC400939041 /* skill_level_45.lottie */,
				281B40492E018FC400939041 /* skill_level_50.lottie */,
				281B404A2E018FC400939041 /* skill_level_55.lottie */,
				281B404B2E018FC400939041 /* skill_level_60.lottie */,
				281B404C2E018FC400939041 /* skill_level_65.lottie */,
				281B404D2E018FC400939041 /* skill_level_70.lottie */,
				281B404E2E018FC400939041 /* skill_level_75.lottie */,
				281B404F2E018FC400939041 /* skill_level_80.lottie */,
				281B40502E018FC400939041 /* skill_level_85.lottie */,
				281B40512E018FC400939041 /* skill_level_90.lottie */,
				281B40522E018FC400939041 /* skill_level_95.lottie */,
				281B40432E013D6100939041 /* skill_level_100.lottie */,
			);
			path = skill_level;
			sourceTree = "<group>";
		};
		281B40642E02D36F00939041 /* Mansion */ = {
			isa = PBXGroup;
			children = (
				281B406A2E02E1E900939041 /* Model */,
				281B40692E02E1E400939041 /* View */,
				281B40652E02D39600939041 /* MansionMainVC.swift */,
				281B40792E042FC500939041 /* MansionMangerListVC.swift */,
			);
			path = Mansion;
			sourceTree = "<group>";
		};
		281B40692E02E1E400939041 /* View */ = {
			isa = PBXGroup;
			children = (
				281B406D2E02E94B00939041 /* MansionAttendantCell.swift */,
				281B406E2E02E94B00939041 /* MansionAttendantSectionView.swift */,
				281B406F2E02E94B00939041 /* MansionHeaderInfoView.swift */,
				281B40702E02E94B00939041 /* MansionMasterSectionView.swift */,
				281B40712E02E94B00939041 /* MansionWorkSectionView.swift */,
				281B40772E03E9DC00939041 /* MansionTaskSheetView.swift */,
				281B407B2E043C3800939041 /* MansionSytemPopView.swift */,
				281B407D2E050EB800939041 /* MansionHisPopView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		281B406A2E02E1E900939041 /* Model */ = {
			isa = PBXGroup;
			children = (
				281B406B2E02E1FC00939041 /* MansionMainModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		281B407F2E05581800939041 /* View */ = {
			isa = PBXGroup;
			children = (
				281B40802E05583900939041 /* RuneDisplayView.swift */,
				281B40822E08E10B00939041 /* RuneDisplayInfoListView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		281B408A2E0A436100939041 /* Model */ = {
			isa = PBXGroup;
			children = (
				281B40882E0A436100939041 /* MenuItemManager.swift */,
				281B40892E0A436100939041 /* MenuItemModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		281C6FD42DFFFF43002F9D99 /* skill_chat */ = {
			isa = PBXGroup;
			children = (
				281C6FCA2DFFFF43002F9D99 /* skill_catch_fail.json */,
				281C6FCB2DFFFF43002F9D99 /* skill_catch_success.json */,
				281C6FCC2DFFFF43002F9D99 /* skill_foot_fail.json */,
				281C6FCD2DFFFF43002F9D99 /* skill_foot_success.json */,
				281C6FCE2DFFFF43002F9D99 /* skill_hole_fail.json */,
				281C6FCF2DFFFF43002F9D99 /* skill_hole_success.json */,
				281C6FD02DFFFF43002F9D99 /* skill_kiss_fail.json */,
				281C6FD12DFFFF43002F9D99 /* skill_kiss_success.json */,
				281C6FD22DFFFF43002F9D99 /* skill_talk_fail.json */,
				281C6FD32DFFFF43002F9D99 /* skill_talk_success.json */,
			);
			path = skill_chat;
			sourceTree = "<group>";
		};
		281C6FDF2E000374002F9D99 /* Until */ = {
			isa = PBXGroup;
			children = (
				281C6FE02E000389002F9D99 /* GameUtils.swift */,
			);
			path = Until;
			sourceTree = "<group>";
		};
		282188902E0E2F5E00362550 /* Screen */ = {
			isa = PBXGroup;
			children = (
				282188912E0E2F7400362550 /* ScreenManagement.swift */,
				2821891E2E0E8E6200362550 /* ZuoJiaMangement.swift */,
			);
			path = Screen;
			sourceTree = "<group>";
		};
		2825A3B32DF6788200EE425C /* TuLong */ = {
			isa = PBXGroup;
			children = (
				2825A3ED2DF952BC00EE425C /* Manager */,
				2825A3D22DF8571A00EE425C /* Model */,
				2825A3B42DF6788800EE425C /* View */,
				2825A3B72DF6B66C00EE425C /* TuLongMainSheetView.swift */,
				2825A3CF2DF852D500EE425C /* TuLongRecordSheetView.swift */,
				2825A3D82DF8617F00EE425C /* TuLongAttMainSheetV.swift */,
				2825A3DA2DF862E500EE425C /* TuLongBaseAttRankListVC.swift */,
				2825A3E42DF9281800EE425C /* TuLongRoomRankListVC.swift */,
				2825A3E62DF92DC700EE425C /* TuLongRoomRankItemVC.swift */,
				2825A3F12DF96DEC00EE425C /* TulongSettlementPopView.swift */,
			);
			path = TuLong;
			sourceTree = "<group>";
		};
		2825A3B42DF6788800EE425C /* View */ = {
			isa = PBXGroup;
			children = (
				2825A3F32DF9721000EE425C /* TuLongSettlementCell.swift */,
				2825A3DE2DF91B7E00EE425C /* TuLongMyRankBarView.swift */,
				2825A3DF2DF91B7E00EE425C /* TuLongRankCell.swift */,
				2825A3E02DF91B7E00EE425C /* TuLongRewardCell.swift */,
				281B40842E09614D00939041 /* TuLongRewardWithNameCell.swift */,
				2825A3D62DF8572D00EE425C /* TuLongRecordEmptyView.swift */,
				2825A3D42DF8572500EE425C /* TuLongRecordCell.swift */,
				2825A3CB2DF8397000EE425C /* CenteredCollectionViewFlowLayout.swift */,
				2825A3B52DF6788F00EE425C /* RedPacketRainView.swift */,
				2825A3C92DF835C700EE425C /* TuLongRedLuckPopView.swift */,
				2825A3EF2DF95AD500EE425C /* TuLongAttCarouseView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2825A3B92DF6B6EE00EE425C /* Task */ = {
			isa = PBXGroup;
			children = (
				2825A3BC2DF6BF0A00EE425C /* Model */,
				2825A3BA2DF6B75B00EE425C /* TaskCarousel.swift */,
				2825A3F52DF99A1D00EE425C /* TaskCell.swift */,
				2825A3BF2DF6BFD100EE425C /* TLCell.swift */,
				2825A3F72DFA650400EE425C /* TaskSurePopView.swift */,
				2825A3FB2DFAAB6E00EE425C /* AdvertView.swift */,
			);
			path = Task;
			sourceTree = "<group>";
		};
		2825A3BC2DF6BF0A00EE425C /* Model */ = {
			isa = PBXGroup;
			children = (
				2825A3BD2DF6BF1400EE425C /* RoomTaskModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2825A3D22DF8571A00EE425C /* Model */ = {
			isa = PBXGroup;
			children = (
				2825A3DC2DF91B2D00EE425C /* TuLongRankModel.swift */,
				2825A3D12DF8571A00EE425C /* TuLongRecordModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2825A3E92DF9527F00EE425C /* View */ = {
			isa = PBXGroup;
			children = (
				2825A3E82DF9527F00EE425C /* UniversalTickerView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2825A3EA2DF9527F00EE425C /* Common */ = {
			isa = PBXGroup;
			children = (
				2825A3E92DF9527F00EE425C /* View */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		2825A3ED2DF952BC00EE425C /* Manager */ = {
			isa = PBXGroup;
			children = (
				2825A3EC2DF952BC00EE425C /* TuLongTickerManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		282804DE2D8831F40015FA35 /* Tag */ = {
			isa = PBXGroup;
			children = (
			);
			path = Tag;
			sourceTree = "<group>";
		};
		282805482D89911B0015FA35 /* UserPage */ = {
			isa = PBXGroup;
			children = (
				2828054A2D8994BC0015FA35 /* VC */,
				282805492D8991270015FA35 /* View */,
			);
			path = UserPage;
			sourceTree = "<group>";
		};
		282805492D8991270015FA35 /* View */ = {
			isa = PBXGroup;
			children = (
				2828054D2D8995220015FA35 /* UserPageHeaderView.swift */,
				282805952D8A63EF0015FA35 /* BasePageInfoView.swift */,
				282805AF2D8A9B220015FA35 /* UserCardPhotoView.swift */,
				282805B12D8AA1460015FA35 /* UserTagCardView.swift */,
				282805AD2D8A99130015FA35 /* UserCardInfoView.swift */,
				282805B32D8AD4BC0015FA35 /* UserPageBottomView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2828054A2D8994BC0015FA35 /* VC */ = {
			isa = PBXGroup;
			children = (
				2828054B2D8994C70015FA35 /* UserPageMainVC.swift */,
				2828054F2D8998920015FA35 /* UserCardListVC.swift */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		282806492D8C0C0B0015FA35 /* Pearls */ = {
			isa = PBXGroup;
			children = (
				2849F8AE2DD5E0D200FA9D53 /* Lv-zi */,
				282806502D8C15360015FA35 /* View */,
				2828064D2D8C124A0015FA35 /* VC */,
				2828064A2D8C12360015FA35 /* Model */,
			);
			path = Pearls;
			sourceTree = "<group>";
		};
		2828064A2D8C12360015FA35 /* Model */ = {
			isa = PBXGroup;
			children = (
				2828064B2D8C12430015FA35 /* PearlsModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2828064D2D8C124A0015FA35 /* VC */ = {
			isa = PBXGroup;
			children = (
				2828064E2D8C12570015FA35 /* PearlsListVC.swift */,
				2849F8B12DD5E20300FA9D53 /* UserLevelMainVC.swift */,
				2849F8B32DD5F1E700FA9D53 /* UserYuanbaoVC.swift */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		282806502D8C15360015FA35 /* View */ = {
			isa = PBXGroup;
			children = (
				282806512D90F42F0015FA35 /* PearlsListCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		282806532D9131990015FA35 /* View */ = {
			isa = PBXGroup;
			children = (
				281B408D2E0A43CC00939041 /* FunctionGridView.swift */,
				281B408E2E0A43CC00939041 /* ServiceMenuView.swift */,
				281B408F2E0A43CC00939041 /* UserHeaderView.swift */,
				282806542D91319F0015FA35 /* RechargeCardView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		282806AC2D92A14F0015FA35 /* NewChat */ = {
			isa = PBXGroup;
			children = (
				283AB18F2DACE8A90086DDB2 /* Manger */,
				282806B52D92B1500015FA35 /* ChatDetail */,
				282806B22D92A3A50015FA35 /* Model */,
				282806AF2D92A3920015FA35 /* View */,
				282806AD2D92A1670015FA35 /* LwChatListVC.swift */,
				283260022D9A3A0B007465A1 /* LwChatMainVC.swift */,
				283260132D9A97C4007465A1 /* HobbyListVC.swift */,
				283260192D9A9FDC007465A1 /* UserGiftListVC.swift */,
				282F07052DA3B86B000EDE0F /* InteractListVC.swift */,
				283263E32D9BC3C9007465A1 /* AddFreiendListVC.swift */,
				287E94142DE56DD7004EEDFB /* CpListSyVC.swift */,
			);
			path = NewChat;
			sourceTree = "<group>";
		};
		282806AF2D92A3920015FA35 /* View */ = {
			isa = PBXGroup;
			children = (
				282806B02D92A3A10015FA35 /* LwChatUserCell.swift */,
				283260152D9A9932007465A1 /* HobbyUserCell.swift */,
				283263DD2D9B85AA007465A1 /* UserGiftListCell.swift */,
				2832601B2D9AA206007465A1 /* NotificationPromptView.swift */,
				283263DF2D9B98B5007465A1 /* ChatStatusPickerView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		282806B22D92A3A50015FA35 /* Model */ = {
			isa = PBXGroup;
			children = (
				282806B32D92A3EE0015FA35 /* LwSessionModel.swift */,
				283260172D9A9A48007465A1 /* HobbyModel.swift */,
				2832601F2D9AA3EE007465A1 /* SyteamModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		282806B52D92B1500015FA35 /* ChatDetail */ = {
			isa = PBXGroup;
			children = (
				282F07112DA6775A000EDE0F /* ChatHelp */,
				28F6433D2D93F2AF00487E43 /* View */,
				282806C32D92B93B0015FA35 /* Model */,
				282806BA2D92B89E0015FA35 /* Cell */,
				282806B62D92B1670015FA35 /* LwChatDetailVC.swift */,
				282F070D2DA61022000EDE0F /* FriendRemarkVC.swift */,
			);
			path = ChatDetail;
			sourceTree = "<group>";
		};
		282806BA2D92B89E0015FA35 /* Cell */ = {
			isa = PBXGroup;
			children = (
				282806BB2D92B8C00015FA35 /* MsgBaseCell.swift */,
				282806BD2D92B8D30015FA35 /* TextMsgCell.swift */,
				282806BF2D92B8F60015FA35 /* ImageMsgCell.swift */,
				282806C12D92B92B0015FA35 /* NoticeMsgCell.swift */,
				282806C62D92D4F10015FA35 /* GiftMsgCell.swift */,
				282806CD2D93D1580015FA35 /* ChatCardCell.swift */,
				28F643EC2D95656D00487E43 /* TacitOptionCell.swift */,
				2832640E2D9D50F7007465A1 /* VoiceCell.swift */,
				287E94102DE54DE2004EEDFB /* RoomInviteMsgCell.swift */,
				281C6FC82DFFFB3D002F9D99 /* UseSkillCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		282806C32D92B93B0015FA35 /* Model */ = {
			isa = PBXGroup;
			children = (
				282806C42D92B9D80015FA35 /* MsgBaseModel.swift */,
				282806C92D92D77F0015FA35 /* GiftModel.swift */,
				28F643402D93F8D200487E43 /* ChatDetailUserModel.swift */,
				28F643EE2D95664800487E43 /* TacitModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		282F07032DA36F7B000EDE0F /* Report */ = {
			isa = PBXGroup;
			children = (
				282F07022DA36F7B000EDE0F /* ReportViewController.swift */,
			);
			path = Report;
			sourceTree = "<group>";
		};
		282F07112DA6775A000EDE0F /* ChatHelp */ = {
			isa = PBXGroup;
			children = (
				282F07122DA67767000EDE0F /* ChatHelp.swift */,
			);
			path = ChatHelp;
			sourceTree = "<group>";
		};
		283260052D9A6EB1007465A1 /* Friends */ = {
			isa = PBXGroup;
			children = (
				2832600D2D9A79D9007465A1 /* Model */,
				283260082D9A6EC9007465A1 /* View */,
				283260062D9A6EC3007465A1 /* LwFriendsMainVC.swift */,
				283260092D9A6ED3007465A1 /* LwFriendsListVC.swift */,
			);
			path = Friends;
			sourceTree = "<group>";
		};
		283260082D9A6EC9007465A1 /* View */ = {
			isa = PBXGroup;
			children = (
				2832600B2D9A6EE8007465A1 /* FriendBaseCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2832600D2D9A79D9007465A1 /* Model */ = {
			isa = PBXGroup;
			children = (
				2832600E2D9A79E8007465A1 /* FriendModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		283260102D9A7E7B007465A1 /* Font */ = {
			isa = PBXGroup;
			children = (
				2825A3CD2DF8409100EE425C /* AlibabaPuHuiTi_2_115_Black.ttf */,
				2825A3C72DF7E0DB00EE425C /* DragonHurt.ttf */,
				283260112D9A7E8E007465A1 /* numFont.ttf */,
			);
			path = Font;
			sourceTree = "<group>";
		};
		283263E92D9BFBA2007465A1 /* Account */ = {
			isa = PBXGroup;
			children = (
				283263EA2D9BFBB9007465A1 /* AccountPhoneVC.swift */,
			);
			path = Account;
			sourceTree = "<group>";
		};
		283263F02D9C0EDC007465A1 /* Real */ = {
			isa = PBXGroup;
			children = (
				283263F32D9C0FD5007465A1 /* RealNameAuthSuccessView.swift */,
				283263F12D9C0F98007465A1 /* RealNameAuthVC.swift */,
				28AFCDC42DB791E5003CE291 /* AuthenticationPopupView.swift */,
			);
			path = Real;
			sourceTree = "<group>";
		};
		283263F92D9C1BA9007465A1 /* Manager */ = {
			isa = PBXGroup;
			children = (
				283263FA2D9C1BB5007465A1 /* PermissionManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		283264032D9CEAFC007465A1 /* Model */ = {
			isa = PBXGroup;
			children = (
				283264022D9CEAFC007465A1 /* DeleteAccountCheckModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2832640D2D9D3AC6007465A1 /* Gift */ = {
			isa = PBXGroup;
			children = (
				287E94972DE83F05004EEDFB /* Tool */,
				283264642D9E244F007465A1 /* View */,
				283264612D9E22C3007465A1 /* Model */,
				2832645F2D9E22AA007465A1 /* GiftCollectionVC.swift */,
				283264672D9E25A5007465A1 /* GiftPanelView.swift */,
			);
			path = Gift;
			sourceTree = "<group>";
		};
		283264612D9E22C3007465A1 /* Model */ = {
			isa = PBXGroup;
			children = (
				283264622D9E22CE007465A1 /* GiftBaseModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		283264642D9E244F007465A1 /* View */ = {
			isa = PBXGroup;
			children = (
				283264652D9E2459007465A1 /* GiftItemCell.swift */,
				283264692D9E26D1007465A1 /* GiftPanelFooterView.swift */,
				2832646B2D9E2848007465A1 /* GiftQuantitySelectionView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2832646D2D9E65AC007465A1 /* MoQI */ = {
			isa = PBXGroup;
			children = (
				28EAA6B32D9E7CC0001600E8 /* MatchQuestionModalView.swift */,
			);
			path = MoQI;
			sourceTree = "<group>";
		};
		283AB18F2DACE8A90086DDB2 /* Manger */ = {
			isa = PBXGroup;
			children = (
				283AB19A2DACFF5B0086DDB2 /* Lottie */,
				283AB1902DACE8B50086DDB2 /* PlayerManager.swift */,
				283AB1922DACEEBF0086DDB2 /* AnimationPlayer.swift */,
				283AB1942DACEF260086DDB2 /* PNGAnimationPlayer.swift */,
				283AB1962DACF2700086DDB2 /* SVGAAnimationPlayer.swift */,
				283AB1982DACFCDE0086DDB2 /* LottieAnimationPlayer.swift */,
				283AB19D2DAD031C0086DDB2 /* VapAnimationPlayer.swift */,
				283AB19F2DAD048F0086DDB2 /* MP4AnimationPlayer.swift */,
				283AB1A32DAD0B190086DDB2 /* AnimationPlayView.swift */,
			);
			path = Manger;
			sourceTree = "<group>";
		};
		283AB19A2DACFF5B0086DDB2 /* Lottie */ = {
			isa = PBXGroup;
			children = (
				283AB19B2DACFF680086DDB2 /* LSZipLottieFileManager.swift */,
				283AB1A12DAD09500086DDB2 /* FileManagerWrapper.swift */,
				2849F8A92DD5BD1A00FA9D53 /* LottieAnimationManager.swift */,
			);
			path = Lottie;
			sourceTree = "<group>";
		};
		283AB2672DAF863F0086DDB2 /* Room */ = {
			isa = PBXGroup;
			children = (
				282188902E0E2F5E00362550 /* Screen */,
				28AFCD5A2DB120AB003CE291 /* Room */,
			);
			path = Room;
			sourceTree = "<group>";
		};
		283AB3482DB0D10B0086DDB2 /* RoomList */ = {
			isa = PBXGroup;
			children = (
				283AB3552DB0DAAA0086DDB2 /* Cells */,
				283AB3502DB0D9270086DDB2 /* Model */,
				283AB3492DB0D1290086DDB2 /* HomeRoomListVC.swift */,
				283AB34B2DB0D1890086DDB2 /* HomeRoomBaseVC.swift */,
			);
			path = RoomList;
			sourceTree = "<group>";
		};
		283AB34D2DB0D52B0086DDB2 /* RoomManger */ = {
			isa = PBXGroup;
			children = (
				28AFCDA82DB5E40F003CE291 /* Model */,
				283AB34E2DB0D5380086DDB2 /* RoomManger.swift */,
				28AFCE062DB8DE71003CE291 /* RoomMsgManger.swift */,
				28AFCE082DB9CA64003CE291 /* TickHub.swift */,
				28AFCE002DB7A7BF003CE291 /* RoomFloatingWindowManager.swift */,
			);
			path = RoomManger;
			sourceTree = "<group>";
		};
		283AB3502DB0D9270086DDB2 /* Model */ = {
			isa = PBXGroup;
			children = (
				283AB3512DB0D9360086DDB2 /* RoomInfoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		283AB3552DB0DAAA0086DDB2 /* Cells */ = {
			isa = PBXGroup;
			children = (
				283AB3532DB0DAAA0086DDB2 /* RoomBigCell.swift */,
				283AB3542DB0DAAA0086DDB2 /* RoomSmallCell.swift */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		283D57042DF28D9A001C4D67 /* Login */ = {
			isa = PBXGroup;
			children = (
				283D57052DF28DA6001C4D67 /* login_new.zip */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		2840EDEA2D83C8EB000A7EA5 /* EditUser */ = {
			isa = PBXGroup;
			children = (
				282804DE2D8831F40015FA35 /* Tag */,
				2840EDF02D83CEFA000A7EA5 /* View */,
				2840EDEF2D83CEF2000A7EA5 /* Model */,
				2840EDEB2D83C8FC000A7EA5 /* EditMainVC.swift */,
				282804DF2D8832150015FA35 /* TagBaseMainVC.swift */,
				282804E12D8832440015FA35 /* TagOptionListVC.swift */,
			);
			path = EditUser;
			sourceTree = "<group>";
		};
		2840EDEF2D83CEF2000A7EA5 /* Model */ = {
			isa = PBXGroup;
			children = (
				2840EDF32D840A6A000A7EA5 /* EditProfileModel.swift */,
				2840EE012D841CDD000A7EA5 /* EditProfileDataProvider.swift */,
				2840EDF12D83CF10000A7EA5 /* UserInfoModel.swift */,
				282804DC2D8817140015FA35 /* TagBaseModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2840EDF02D83CEFA000A7EA5 /* View */ = {
			isa = PBXGroup;
			children = (
				2840EDF62D840A77000A7EA5 /* BaseEditProfileCell.swift */,
				2840EDF92D840B54000A7EA5 /* EditPhotoCell.swift */,
				2840EDFB2D840B7C000A7EA5 /* EditTextInfoCell.swift */,
				2840EDFD2D840BAA000A7EA5 /* EditUserAlbumCell.swift */,
				2840EDFF2D840BC6000A7EA5 /* EditTagListCell.swift */,
				2840EE032D8431BD000A7EA5 /* EditUserTextSheetView.swift */,
				282804DA2D880C540015FA35 /* EditTagSeletedSheetView.swift */,
				282805462D8984620015FA35 /* EditAdressSheetView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2849F88D2DD31A9F00FA9D53 /* FloatingScreen */ = {
			isa = PBXGroup;
			children = (
				2849F88E2DD31AD300FA9D53 /* FloatingScreenView.swift */,
			);
			path = FloatingScreen;
			sourceTree = "<group>";
		};
		2849F8962DD3717600FA9D53 /* Skill */ = {
			isa = PBXGroup;
			children = (
				285DA64F2DFBFF1B00BDCDAD /* Pk */,
				2849F8A22DD46E8B00FA9D53 /* Model */,
				2849F89B2DD4391D00FA9D53 /* View */,
				2849F8972DD3718700FA9D53 /* SkillMainListVC.swift */,
				2849F8992DD3719700FA9D53 /* SkillListVC.swift */,
			);
			path = Skill;
			sourceTree = "<group>";
		};
		2849F89B2DD4391D00FA9D53 /* View */ = {
			isa = PBXGroup;
			children = (
				2849F89C2DD4392400FA9D53 /* LipPowerView.swift */,
				2849F89E2DD43A9000FA9D53 /* UpgradeCostView.swift */,
				2849F8A02DD43EAE00FA9D53 /* PointsExchangeView.swift */,
				2849F8A72DD597CB00FA9D53 /* SkillLevelRatingView.swift */,
				28C1ED0A2DD72CE1006F0442 /* TuPoView.swift */,
				28C1ED0C2DD72CF6006F0442 /* UpgradeConditionView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2849F8A22DD46E8B00FA9D53 /* Model */ = {
			isa = PBXGroup;
			children = (
				2849F8A32DD46E9D00FA9D53 /* SkillItemModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2849F8AB2DD5C25900FA9D53 /* Skill */ = {
			isa = PBXGroup;
			children = (
				281B40472E018FA600939041 /* skill_level */,
				281C6FD42DFFFF43002F9D99 /* skill_chat */,
			);
			path = Skill;
			sourceTree = "<group>";
		};
		2849F8AE2DD5E0D200FA9D53 /* Lv-zi */ = {
			isa = PBXGroup;
			children = (
				2849F8AF2DD5E0EC00FA9D53 /* LuZiPaySheetView.swift */,
			);
			path = "Lv-zi";
			sourceTree = "<group>";
		};
		28553F4E2D87C3660042C619 /* Layout */ = {
			isa = PBXGroup;
			children = (
				28553F4F2D87C3710042C619 /* CommonAlignFlowLayout.swift */,
			);
			path = Layout;
			sourceTree = "<group>";
		};
		285DA6462DFAC1DE00BDCDAD /* Red */ = {
			isa = PBXGroup;
			children = (
				285DA6472DFAC1EB00BDCDAD /* RedActView.swift */,
				285DA6492DFACFF500BDCDAD /* BossRedRobView.swift */,
				285DA64B2DFAF02900BDCDAD /* BossRedRedBegainV.swift */,
				285DA64D2DFAFD9600BDCDAD /* RedLuckUserPopV.swift */,
			);
			path = Red;
			sourceTree = "<group>";
		};
		285DA64F2DFBFF1B00BDCDAD /* Pk */ = {
			isa = PBXGroup;
			children = (
				281B407F2E05581800939041 /* View */,
				285DA6522DFC1D1000BDCDAD /* Model */,
				285DA6502DFBFF2900BDCDAD /* UserPkSheetView.swift */,
				281B40452E01838500939041 /* PkResultPopView.swift */,
			);
			path = Pk;
			sourceTree = "<group>";
		};
		285DA6522DFC1D1000BDCDAD /* Model */ = {
			isa = PBXGroup;
			children = (
				285DA6532DFC1D1A00BDCDAD /* SkillInfoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		2866EB7E2DC1D71A005B0D13 /* User */ = {
			isa = PBXGroup;
			children = (
				2866EB7F2DC1D743005B0D13 /* RoomUserMainSheetV.swift */,
				2866EB812DC1D75B005B0D13 /* OnlineListVC.swift */,
				2866EB832DC1D766005B0D13 /* RankListVC.swift */,
			);
			path = User;
			sourceTree = "<group>";
		};
		28785FE72E0A8B3D00367787 /* MainGoods */ = {
			isa = PBXGroup;
			children = (
				287860022E0BF60800367787 /* MyPack */,
				28785FED2E0A96DC00367787 /* Model */,
				28785FEA2E0A91F100367787 /* View */,
				28785FE82E0A8B4B00367787 /* MainGoodsVC.swift */,
				28785FF62E0ABC5C00367787 /* HotGoodsListVC.swift */,
				28785FF02E0A9A1600367787 /* DecorationMainVC.swift */,
				28785FF22E0A9A2800367787 /* DecorationListVC.swift */,
				28785FF42E0AAFA200367787 /* RingListVC.swift */,
			);
			path = MainGoods;
			sourceTree = "<group>";
		};
		28785FEA2E0A91F100367787 /* View */ = {
			isa = PBXGroup;
			children = (
				28785FEB2E0A91FB00367787 /* DressUpListCell.swift */,
				28785FF82E0AC85F00367787 /* DressBuyPopView.swift */,
				28785FFE2E0BDB5300367787 /* EventPreviewShowView.swift */,
				28785FFA2E0B887200367787 /* ShowSpecialView.swift */,
				287860002E0BEBA200367787 /* PurchaseSuccessView.swift */,
				287860072E0C0F8E00367787 /* WearPrePopView.swift */,
				287860092E0C1D7B00367787 /* RingBuyView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		28785FED2E0A96DC00367787 /* Model */ = {
			isa = PBXGroup;
			children = (
				28785FEE2E0A970800367787 /* DressBaseModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		287860022E0BF60800367787 /* MyPack */ = {
			isa = PBXGroup;
			children = (
				287860032E0BF63700367787 /* MyPackMainVC.swift */,
				287860052E0BF64F00367787 /* MyPackListVC.swift */,
			);
			path = MyPack;
			sourceTree = "<group>";
		};
		287E93522DDEB637004EEDFB /* Tool */ = {
			isa = PBXGroup;
			children = (
				287E94042DE45D78004EEDFB /* Song */,
				287E93532DDEB655004EEDFB /* RoomNoticeEditVC.swift */,
				287E93552DDEF71D004EEDFB /* RoomAdminListVC.swift */,
				287E93572DDEF728004EEDFB /* RoomAdminAddVC.swift */,
				287E94002DE3FF0D004EEDFB /* RoomBgListVC.swift */,
				287E94022DE43C9E004EEDFB /* RoomGiftOpenSheetView.swift */,
				287E941C2DE5BCA7004EEDFB /* InvterFansPopView.swift */,
				287E948B2DE5D717004EEDFB /* RoomInvterUserSheetView.swift */,
				287E948D2DE6B123004EEDFB /* RoomLockPopView.swift */,
				287E948F2DE6F0D6004EEDFB /* RoomSettingSheetView.swift */,
				28EB87E82DEED09200D389FB /* RoomEditTitleView.swift */,
				28EB87EE2DF0440000D389FB /* HotInfoPopupView.swift */,
			);
			path = Tool;
			sourceTree = "<group>";
		};
		287E93F72DE06F05004EEDFB /* GiftQue */ = {
			isa = PBXGroup;
			children = (
				287E93F82DE06F1A004EEDFB /* GiftQueManager.swift */,
				287E93FA2DE06F4A004EEDFB /* QueGift.swift */,
				287E93FC2DE06F78004EEDFB /* GiftShowView.swift */,
				287E93FE2DE06FA8004EEDFB /* GiftNumView.swift */,
			);
			path = GiftQue;
			sourceTree = "<group>";
		};
		287E94042DE45D78004EEDFB /* Song */ = {
			isa = PBXGroup;
			children = (
				28EB87F12DF1383D00D389FB /* View */,
				287E94092DE46497004EEDFB /* Tool */,
				287E94052DE45DA2004EEDFB /* RoomSongMainView.swift */,
				287E94072DE463B8004EEDFB /* RoomSongListVC.swift */,
				287E940C2DE4722D004EEDFB /* MusicPlayView.swift */,
				287E940E2DE47358004EEDFB /* RoomSongCell.swift */,
			);
			path = Song;
			sourceTree = "<group>";
		};
		287E94092DE46497004EEDFB /* Tool */ = {
			isa = PBXGroup;
			children = (
				287E940A2DE46823004EEDFB /* MusicPlaybackManager.swift */,
			);
			path = Tool;
			sourceTree = "<group>";
		};
		287E94972DE83F05004EEDFB /* Tool */ = {
			isa = PBXGroup;
			children = (
				287E94982DE8423F004EEDFB /* GiftUserListBar.swift */,
			);
			path = Tool;
			sourceTree = "<group>";
		};
		288548E42D6F3F0A0047A69D /* PassWord */ = {
			isa = PBXGroup;
			children = (
				288548F02D7019EF0047A69D /* PasswordLoginVC.swift */,
				288548E52D6F3F200047A69D /* PassWordAccountVC.swift */,
			);
			path = PassWord;
			sourceTree = "<group>";
		};
		288548E72D6FF8130047A69D /* ChatManger */ = {
			isa = PBXGroup;
			children = (
				288548E82D6FF8260047A69D /* T_IMHelper.swift */,
				28F643DC2D9535F700487E43 /* SendMsg-Ext.swift */,
				282806B82D92B29C0015FA35 /* MessageHandlerProtocol.swift */,
			);
			path = ChatManger;
			sourceTree = "<group>";
		};
		288548F42D718CDA0047A69D /* View */ = {
			isa = PBXGroup;
			children = (
				2885494E2D7938110047A69D /* emoji */,
				288548F52D718CE70047A69D /* InfoItemBaseView.swift */,
				288548F72D719D9E0047A69D /* BaseBannerView.swift */,
				2885491B2D759C750047A69D /* TagLabelView.swift */,
				2885493C2D782AF80047A69D /* LWLoadingView.swift */,
				2885493E2D782C520047A69D /* LodingRefeshHeaderView.swift */,
				288549422D783DD80047A69D /* SVGAnimationPlayer.swift */,
				28B932D62D81602000DE4F8E /* MessagePopupView.swift */,
				2840B2852D828629007C907C /* GenericActionSheetView.swift */,
				283263E12D9BBF37007465A1 /* PopoverMenuViewController.swift */,
				28AFCF9E2DBF5A03003CE291 /* TagListView.swift */,
				289F9E9F2DCC98E100D7522D /* CustomProgressBar.swift */,
				2825A3C52DF7C8A200EE425C /* SheetWkWebView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		288548F92D71A2CB0047A69D /* View */ = {
			isa = PBXGroup;
			children = (
				288549492D787A310047A69D /* Input */,
				288548FA2D71A2DB0047A69D /* DyHeaderView.swift */,
				288548FF2D71B8EB0047A69D /* TopicCell.swift */,
				2885490F2D755D8E0047A69D /* ImageExpandingView.swift */,
				288549112D75909D0047A69D /* DynamicBaseCell.swift */,
				288549192D7598760047A69D /* ImageCardView.swift */,
				288549402D783A390047A69D /* VoiceBaseCardView.swift */,
				2885491F2D7698BF0047A69D /* DynamicDetailHeaderView.swift */,
				282F07072DA4B45B000EDE0F /* UserDynamicCell.swift */,
				28B932352D7ED83100DE4F8E /* DySelUserCell.swift */,
				288549212D76D9BC0047A69D /* RemarkTopCell.swift */,
				288549252D76E0F10047A69D /* CommentOneCell.swift */,
				288549342D77EE780047A69D /* FollowNodataView.swift */,
				2840EDA52D82F32A000A7EA5 /* DyNoticeCell.swift */,
				28785FFC2E0BA49400367787 /* BaseChatBgView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		288548FC2D71A5EA0047A69D /* Model */ = {
			isa = PBXGroup;
			children = (
				288548FD2D71A5F30047A69D /* BannerModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		288549012D71B9B00047A69D /* Model */ = {
			isa = PBXGroup;
			children = (
				288549022D71B9B50047A69D /* TopicModel.swift */,
				288549042D753BFB0047A69D /* ExpandPushAnimator.swift */,
				288549132D7594A40047A69D /* DynamicBaseModel.swift */,
				288549232D76E04B0047A69D /* CommentModel.swift */,
				2840EDA32D82F248000A7EA5 /* DynoticeModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		288549062D7541050047A69D /* ViewController */ = {
			isa = PBXGroup;
			children = (
				2885492C2D77248C0047A69D /* LocationManager.swift */,
				288549092D7541280047A69D /* TopicDetailVC.swift */,
				2885490B2D75522C0047A69D /* CreatePostVC.swift */,
				2885490D2D7554B80047A69D /* TopicSelListVC.swift */,
				288549152D7595150047A69D /* DynamicMainVC.swift */,
				288549322D77EB330047A69D /* DynamicFollowVC.swift */,
				288549172D75954F0047A69D /* DynamicBaseListVC.swift */,
				288549362D77FAC20047A69D /* DynamicNewListVC.swift */,
				288549382D77FB380047A69D /* DynamicLocListVC.swift */,
				282F07092DA4B587000EDE0F /* UserListDynamicVC.swift */,
				2885491D2D7697480047A69D /* DynamicDetailVC.swift */,
				28B932332D7ED6F900DE4F8E /* DySelUserlistVC.swift */,
				2840EDA12D82E68D000A7EA5 /* DynamicNoticeVC.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		288549292D7723220047A69D /* Source */ = {
			isa = PBXGroup;
			children = (
				283D57042DF28D9A001C4D67 /* Login */,
				2849F8AB2DD5C25900FA9D53 /* Skill */,
				289F9E8A2DCB0D2600D7522D /* Boss */,
				283260102D9A7E7B007465A1 /* Font */,
				2885494C2D7937E30047A69D /* emoji_mapping.json */,
				281B40672E02DA1400939041 /* work_new_select.svga */,
				288549442D7843330047A69D /* dy_voicePlay.svga */,
				2885493A2D7829180047A69D /* loading2.json */,
				288549302D7729A40047A69D /* le_city.json */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		288549462D7846F40047A69D /* Audio */ = {
			isa = PBXGroup;
			children = (
				28F643E72D953AEA00487E43 /* AudioRecordView */,
				288549472D7846FD0047A69D /* MediaPlaybackManager.swift */,
				28B931C92D7AF93900DE4F8E /* AudioRecordSession.swift */,
			);
			path = Audio;
			sourceTree = "<group>";
		};
		288549492D787A310047A69D /* Input */ = {
			isa = PBXGroup;
			children = (
				2885494A2D787A490047A69D /* DyInputView.swift */,
				28B931C52D7ACD9900DE4F8E /* DyPostInputView.swift */,
				28B931C72D7AF12800DE4F8E /* VoicePostView.swift */,
			);
			path = Input;
			sourceTree = "<group>";
		};
		2885494E2D7938110047A69D /* emoji */ = {
			isa = PBXGroup;
			children = (
				2885494F2D7938300047A69D /* EmojiModel.swift */,
				288549512D7938530047A69D /* EmojiManger.swift */,
				288549532D793C370047A69D /* EmojiKeyboardVC.swift */,
				288549552D793E0E0047A69D /* EmojiMainView.swift */,
				2885499D2D79BB6D0047A69D /* EmojiTextView.swift */,
			);
			path = emoji;
			sourceTree = "<group>";
		};
		289F9E382DCA026D00D7522D /* Act */ = {
			isa = PBXGroup;
			children = (
				289F9E392DCA027D00D7522D /* VoiceRoomActivityCell.swift */,
			);
			path = Act;
			sourceTree = "<group>";
		};
		289F9E872DCB022C00D7522D /* NianBoss */ = {
			isa = PBXGroup;
			children = (
				289F9E982DCC5A1200D7522D /* Model */,
				289F9E952DCC59E400D7522D /* View */,
				289F9E882DCB0C9000D7522D /* BossAttackFloatingView.swift */,
				2849F8942DD3539600FA9D53 /* BossAttackManager.swift */,
				289F9E932DCC4E7000D7522D /* BoosMainSheetView.swift */,
				2849F8902DD31CD000FA9D53 /* BossChestStatusView.swift */,
				289F9EAD2DCCD3B600D7522D /* BossBuyBookSheetView.swift */,
				289F9F6A2DCDEE6000D7522D /* StackedNotificationView.swift */,
				28BB749D2DD19D44004B9997 /* BossJpMainSheetView.swift */,
				28BB749F2DD1CDCF004B9997 /* BossJpListVC.swift */,
				28BB74A52DD1E925004B9997 /* BossMoreTextView.swift */,
				28BB74A72DD1ED0F004B9997 /* BossGoodsdhSheetView.swift */,
				28BB74AB2DD1FDDF004B9997 /* BossMyRateView.swift */,
				287E949A2DE87AEA004EEDFB /* BossTiaoJIanSheetView.swift */,
			);
			path = NianBoss;
			sourceTree = "<group>";
		};
		289F9E8A2DCB0D2600D7522D /* Boss */ = {
			isa = PBXGroup;
			children = (
				2825A3C12DF6D14700EE425C /* dragon_enter.mp4 */,
				2825A3C22DF6D14700EE425C /* dragon_normal.mp4 */,
				289F9EA92DCCAC9F00D7522D /* boss_attPath.svga */,
				289F9EAA2DCCAC9F00D7522D /* boss_boom.svga */,
				289F9EA72DCCAB6600D7522D /* putongMF.mp4 */,
				289F9EA52DCC9F4200D7522D /* boss_box.zip */,
				289F9E9D2DCC929200D7522D /* gaojiMofa.mp4 */,
				289F9E9B2DCC5C6400D7522D /* boss_fazhen.svga */,
				289F9E8B2DCB0D7D00D7522D /* boss_max_aida.svga */,
				289F9E8C2DCB0D7D00D7522D /* boss_max_xh.svga */,
				289F9E8D2DCB0D7D00D7522D /* boss_mini_aida.svga */,
				289F9E8E2DCB0D7D00D7522D /* boss_mini_xh.svga */,
			);
			path = Boss;
			sourceTree = "<group>";
		};
		289F9E952DCC59E400D7522D /* View */ = {
			isa = PBXGroup;
			children = (
				289F9E962DCC59F000D7522D /* BossRecordTickerView.swift */,
				289F9F682DCDA50F00D7522D /* BossValueView.swift */,
				28BB74A12DD1D31F004B9997 /* JiangPingCell.swift */,
				28BB74A32DD1D348004B9997 /* JingLiCell.swift */,
				28BB74A92DD1F7F1004B9997 /* BossGoodsCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		289F9E982DCC5A1200D7522D /* Model */ = {
			isa = PBXGroup;
			children = (
				289F9E992DCC5A4100D7522D /* BossRecordRotation.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		28AFCD5A2DB120AB003CE291 /* Room */ = {
			isa = PBXGroup;
			children = (
				2825A3EA2DF9527F00EE425C /* Common */,
				2825A3B92DF6B6EE00EE425C /* Task */,
				2825A3B32DF6788200EE425C /* TuLong */,
				287E93522DDEB637004EEDFB /* Tool */,
				289F9E872DCB022C00D7522D /* NianBoss */,
				28AFCF962DBE3A95003CE291 /* Card */,
				28AFCE772DBA1230003CE291 /* ViewModel */,
				28AFCDFD2DB79CC1003CE291 /* RoomFloating */,
				28AFCDB72DB6670B003CE291 /* Model */,
				28AFCDB22DB654BD003CE291 /* View */,
				28AFCDAB2DB5F730003CE291 /* Seat */,
				28AFCD5B2DB120C2003CE291 /* HotRoomMainVC.swift */,
				28AFCDBC2DB73070003CE291 /* CreateRoomVC.swift */,
				28AFCE7A2DBA127D003CE291 /* RoomPublicMessageVC.swift */,
				28E785302DC9AD440092CED9 /* AudienceRoomClosedVC.swift */,
				28E785322DC9BA590092CED9 /* RoomSummaryVC.swift */,
			);
			path = Room;
			sourceTree = "<group>";
		};
		28AFCDA82DB5E40F003CE291 /* Model */ = {
			isa = PBXGroup;
			children = (
				28AFCDA92DB5E41B003CE291 /* RoomSnapshot.swift */,
				28AFCDC02DB73945003CE291 /* RoomTypeModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		28AFCDAB2DB5F730003CE291 /* Seat */ = {
			isa = PBXGroup;
			children = (
				28AFCDAC2DB5F740003CE291 /* RoomSeatView.swift */,
				28AFCE812DBA2D56003CE291 /* RoomSeatConfiguration.swift */,
				28AFCDAE2DB5F790003CE291 /* RoomContainerView.swift */,
				28AFCDBA2DB72430003CE291 /* AudioWaveView.swift */,
				28EB87EC2DF0214200D389FB /* RoomSeatDegreeView.swift */,
			);
			path = Seat;
			sourceTree = "<group>";
		};
		28AFCDB22DB654BD003CE291 /* View */ = {
			isa = PBXGroup;
			children = (
				285DA6462DFAC1DE00BDCDAD /* Red */,
				28C1ED1D2DDB2E0B006F0442 /* Msg */,
				28AFCF432DBDC82B003CE291 /* Input */,
				28AFCDB32DB654C2003CE291 /* SideMenuView.swift */,
				28AFCDB52DB65ABE003CE291 /* RoomInfoTopView.swift */,
				28AFCEF32DBB6882003CE291 /* RoomNavUserView.swift */,
				28AFCDBE2DB730C3003CE291 /* RoomTypeCell.swift */,
				28AFCDC22DB73AA4003CE291 /* GameTypeCell.swift */,
				28AFCE022DB7B776003CE291 /* RoomBgView.swift */,
				28AFCE042DB88728003CE291 /* AvatarFrameView.swift */,
				28AFCE852DBA45A2003CE291 /* RoomMessageCell.swift */,
				2849F8A52DD48F3900FA9D53 /* RoomHtmlMsgCell.swift */,
				28AFCF3B2DBB9353003CE291 /* RoomChatBarView.swift */,
				2825A3F92DFA844700EE425C /* ExitRoomTaskPopView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		28AFCDB72DB6670B003CE291 /* Model */ = {
			isa = PBXGroup;
			children = (
				28AFCDB82DB66717003CE291 /* RoomDetailModel.swift */,
				28AFCE7C2DBA12A3003CE291 /* RoomBaseMsgModel.swift */,
				28AFCE832DBA4395003CE291 /* RoomMsgType.swift */,
				28AFCF102DBB7297003CE291 /* RoomUserInfoModel.swift */,
				2825A4002DFABE3900EE425C /* RedBagBaseModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		28AFCDFD2DB79CC1003CE291 /* RoomFloating */ = {
			isa = PBXGroup;
			children = (
				28AFCDFE2DB79CD4003CE291 /* RoomFloatingWindow.swift */,
			);
			path = RoomFloating;
			sourceTree = "<group>";
		};
		28AFCE772DBA1230003CE291 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				28AFCE782DBA123A003CE291 /* RoomViewModel.swift */,
				28AFCFCB2DBF8B49003CE291 /* SeatActionManager.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		28AFCF432DBDC82B003CE291 /* Input */ = {
			isa = PBXGroup;
			children = (
				287E94912DE70195004EEDFB /* RoomChatInputPopupView.swift */,
				28AFCF462DBDC96E003CE291 /* CircleProgressView.swift */,
				28AFCF482DBDCD3A003CE291 /* LuckSproutButton.swift */,
				2825A3FD2DFAB40900EE425C /* ComboButton.swift */,
			);
			path = Input;
			sourceTree = "<group>";
		};
		28AFCF962DBE3A95003CE291 /* Card */ = {
			isa = PBXGroup;
			children = (
				289F9E382DCA026D00D7522D /* Act */,
				2866EB7E2DC1D71A005B0D13 /* User */,
				28AFCF972DBE3AA2003CE291 /* RoomInfoCardSheetView.swift */,
				28AFCFDA2DC0CD91003CE291 /* RoomDetailSheetView.swift */,
				289F9E032DC9F7D300D7522D /* RoomToolSheetView.swift */,
				289F9E3B2DCA02BD00D7522D /* VoiceRoomActivitySheetView.swift */,
				28EB87EA2DEFEB8000D389FB /* RoomTypeSelectionView.swift */,
			);
			path = Card;
			sourceTree = "<group>";
		};
		28B931C22D7A8F5000DE4F8E /* inputView */ = {
			isa = PBXGroup;
			children = (
				28B931C32D7A8F6000DE4F8E /* ChatInputView.swift */,
			);
			path = inputView;
			sourceTree = "<group>";
		};
		28B932922D81283300DE4F8E /* Jump */ = {
			isa = PBXGroup;
			children = (
				28B932932D81367B00DE4F8E /* LWJumpManger.swift */,
			);
			path = Jump;
			sourceTree = "<group>";
		};
		28C1ED0E2DD73E4F006F0442 /* SkillGoods */ = {
			isa = PBXGroup;
			children = (
				28C1ED172DDAC203006F0442 /* View */,
				28C1ED132DD74293006F0442 /* Model */,
				28C1ED0F2DD73E68006F0442 /* SkillGoodsMainVC.swift */,
				28C1ED112DD73E71006F0442 /* SkillGoodsListVC.swift */,
			);
			path = SkillGoods;
			sourceTree = "<group>";
		};
		28C1ED132DD74293006F0442 /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			path = Model;
			sourceTree = "<group>";
		};
		28C1ED172DDAC203006F0442 /* View */ = {
			isa = PBXGroup;
			children = (
				28C1ED162DDAC203006F0442 /* CurrencyBottomView.swift */,
				28C1ED192DDACDF0006F0442 /* GoodsCell.swift */,
				28C1ED1B2DDADF9E006F0442 /* GoodsBuyPopView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		28C1ED1D2DDB2E0B006F0442 /* Msg */ = {
			isa = PBXGroup;
			children = (
				28C1ED1E2DDB2E1E006F0442 /* RoomTextMsgCell.swift */,
			);
			path = Msg;
			sourceTree = "<group>";
		};
		28E21A682D6C66D90007ED12 /* Main */ = {
			isa = PBXGroup;
			children = (
				287E93F72DE06F05004EEDFB /* GiftQue */,
				28B932922D81283300DE4F8E /* Jump */,
				28E21AA52D6DC05D0007ED12 /* Base */,
				28E21A692D6C67BE0007ED12 /* Manger */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		28E21A692D6C67BE0007ED12 /* Manger */ = {
			isa = PBXGroup;
			children = (
				283AB34D2DB0D52B0086DDB2 /* RoomManger */,
				288549462D7846F40047A69D /* Audio */,
				288548E72D6FF8130047A69D /* ChatManger */,
				28E21AA02D6DA0A50007ED12 /* Oss */,
				28E21A852D6D6B8F0007ED12 /* QuickLogin */,
				28E21A782D6C7D920007ED12 /* SM_TW */,
				28E21A6D2D6C686D0007ED12 /* Until */,
				28E21A6A2D6C67C50007ED12 /* NetTool */,
			);
			path = Manger;
			sourceTree = "<group>";
		};
		28E21A6A2D6C67C50007ED12 /* NetTool */ = {
			isa = PBXGroup;
			children = (
				28E21A6B2D6C68280007ED12 /* EnvManager.swift */,
				28E21A702D6C6AF30007ED12 /* ApiResponse.swift */,
				28E21A742D6C6F530007ED12 /* ApiService.swift */,
				28E21A762D6C706B0007ED12 /* NetworkUtility.swift */,
				28E21A7B2D6C7DF40007ED12 /* YINDONG-Bridging-Header.h */,
			);
			path = NetTool;
			sourceTree = "<group>";
		};
		28E21A6D2D6C686D0007ED12 /* Until */ = {
			isa = PBXGroup;
			children = (
				28E21A6E2D6C687F0007ED12 /* AppTool.swift */,
				28E21A722D6C6C340007ED12 /* AppConfigUI_Ext.swift */,
				28E21A9C2D6D9B550007ED12 /* Notification_Ext.swift */,
				288549272D76FA020047A69D /* String_Ext.swift */,
				28B932392D801D8900DE4F8E /* RateLimitManager.swift */,
			);
			path = Until;
			sourceTree = "<group>";
		};
		28E21A782D6C7D920007ED12 /* SM_TW */ = {
			isa = PBXGroup;
			children = (
				28E21A792D6C7DAC0007ED12 /* SmAntiFraud.xcframework */,
			);
			path = SM_TW;
			sourceTree = "<group>";
		};
		28E21A852D6D6B8F0007ED12 /* QuickLogin */ = {
			isa = PBXGroup;
			children = (
				28E21A8E2D6D6BF40007ED12 /* ATAuthSDK.framework */,
				28E21A8F2D6D6BF40007ED12 /* YTXMonitor.framework */,
				28E21A902D6D6BF40007ED12 /* YTXOperators.framework */,
			);
			path = QuickLogin;
			sourceTree = "<group>";
		};
		28E21A942D6D6C040007ED12 /* Manger */ = {
			isa = PBXGroup;
			children = (
				28E21A952D6D6C1B0007ED12 /* QuickManger.swift */,
			);
			path = Manger;
			sourceTree = "<group>";
		};
		28E21A992D6D984E0007ED12 /* View */ = {
			isa = PBXGroup;
			children = (
				28E21A9A2D6D98590007ED12 /* PrivacySettingsSheetView.swift */,
				28E21A9E2D6D9E510007ED12 /* ThirdQuickView.swift */,
				28E21AA62D6DC1470007ED12 /* QuickLoginView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		28E21AA02D6DA0A50007ED12 /* Oss */ = {
			isa = PBXGroup;
			children = (
				28E21AA12D6DA0AC0007ED12 /* AliyunOssHelper.swift */,
			);
			path = Oss;
			sourceTree = "<group>";
		};
		28E21AA52D6DC05D0007ED12 /* Base */ = {
			isa = PBXGroup;
			children = (
				28553F4E2D87C3660042C619 /* Layout */,
				288549292D7723220047A69D /* Source */,
				288548FC2D71A5EA0047A69D /* Model */,
				288548F42D718CDA0047A69D /* View */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		28E21AAA2D6EB1890007ED12 /* Model */ = {
			isa = PBXGroup;
			children = (
				28E21AAB2D6EB1930007ED12 /* LoginUserModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		28EB87F12DF1383D00D389FB /* View */ = {
			isa = PBXGroup;
			children = (
				28EB87F02DF1383D00D389FB /* VolumePopupView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		28F6433D2D93F2AF00487E43 /* View */ = {
			isa = PBXGroup;
			children = (
				2832646D2D9E65AC007465A1 /* MoQI */,
				2832640D2D9D3AC6007465A1 /* Gift */,
				28F6433E2D93F2BE00487E43 /* ChatDetailNavView.swift */,
				28F643F02D967DBC00487E43 /* TacitOptionCardSheetView.swift */,
				282F070B2DA60C05000EDE0F /* ChatWarningView.swift */,
				282F070F2DA6206A000EDE0F /* IntimacyTipPopView.swift */,
				287E94122DE55F79004EEDFB /* RoomItemView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		28F643E72D953AEA00487E43 /* AudioRecordView */ = {
			isa = PBXGroup;
			children = (
				959B2AFF2CE8520C0047E096 /* AudioRecordingHUDView.swift */,
				28F643E82D953B0700487E43 /* AudioRecordingProgressView.swift */,
				28F643EA2D953B1B00487E43 /* AudioVisualizerView.swift */,
			);
			path = AudioRecordView;
			sourceTree = "<group>";
		};
		2FC1011CED08D77A8577C23E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				28E21A8C2D6D6BAA0007ED12 /* Network.framework */,
				28E21A832D6C803D0007ED12 /* CoreLocation.framework */,
				28E21A812D6C802B0007ED12 /* IOKit.framework */,
				28E21A7F2D6C7FE30007ED12 /* CoreAudioTypes.framework */,
				6F053B6ABFE3B6136BECD374 /* Pods_YINDONG.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7AB5B925D5A5E504DB552263 /* Pods */ = {
			isa = PBXGroup;
			children = (
				80AB014BE5AAB7F6B13EA95F /* Pods-YINDONG.debug.xcconfig */,
				6AFDABB36EC1DFB76A8E2A8B /* Pods-YINDONG.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		9541881A2D055ABF0043071D /* Manager */ = {
			isa = PBXGroup;
			children = (
				9541881F2D0561F90043071D /* BlacklistManager.swift */,
				9541881E2D0561F90043071D /* ChatUserManager.swift */,
				954188192D055ABF0043071D /* ChatAudioManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		956795512CFF529300F03EF8 /* Views */ = {
			isa = PBXGroup;
			children = (
				9541881C2D055B520043071D /* MessageCell.swift */,
				9567954F2CFF529300F03EF8 /* ChatListCell.swift */,
				2832601D2D9AA3BB007465A1 /* SyteamCell.swift */,
				287E94162DE5931D004EEDFB /* CpManeuverCell.swift */,
				************************ /* InvterRoomCell.swift */,
				287E941A2DE5B20D004EEDFB /* SyTextLinkCell.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		956795542CFF529300F03EF8 /* Models */ = {
			isa = PBXGroup;
			children = (
				954188172D0558990043071D /* MessageStorage.swift */,
				956795522CFF529300F03EF8 /* ConversationModel.swift */,
				956795532CFF529300F03EF8 /* MessageModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		9567955B2CFF711100F03EF8 /* Auth */ = {
			isa = PBXGroup;
			children = (
				288548E42D6F3F0A0047A69D /* PassWord */,
				28E21AAA2D6EB1890007ED12 /* Model */,
				28E21A992D6D984E0007ED12 /* View */,
				28E21A942D6D6C040007ED12 /* Manger */,
				954188262D0579200043071D /* WebViewController.swift */,
				956795592CFF711100F03EF8 /* LoginVC.swift */,
				28E21AA32D6DAC030007ED12 /* ProfileSetupVC.swift */,
				28E21AA82D6DD7100007ED12 /* SendCodeVC.swift */,
				28E21AAF2D6EED690007ED12 /* PasswordSettingVC.swift */,
				288548F22D7073D80047A69D /* ThirdBindPhoneVC.swift */,
				28E21AB12D6EF1AD0007ED12 /* FeedbackVC.swift */,
				28E21AAD2D6EB5C00007ED12 /* SelectAccountVC.swift */,
				288548EE2D70065A0047A69D /* WaitLuanchVC.swift */,
			);
			path = Auth;
			sourceTree = "<group>";
		};
		9567955F2CFF711F00F03EF8 /* Models */ = {
			isa = PBXGroup;
			children = (
				281C6FDF2E000374002F9D99 /* Until */,
				95DAA5B82D2D79E100126D4E /* BlockedUserManager.swift */,
				95DAA5B02D2D732300126D4E /* MusicianModel.swift */,
				95DAA5B42D2D789600126D4E /* ReportModel.swift */,
				95DAA59C2D2D6CA500126D4E /* PostModel.swift */,
				9567955E2CFF711F00F03EF8 /* UserModel.swift */,
				28785FE52E0A7D9900367787 /* UserInfoCollectionModel.swift */,
				288548EA2D7002530047A69D /* LWUserModel.swift */,
				288548EC2D7002F30047A69D /* LWUserManger.swift */,
				28AFCF9C2DBF34CB003CE291 /* AppConfigManger.swift */,
				282806562D9136D60015FA35 /* UserRechargeModel.swift */,
				282806582D9138910015FA35 /* UserLevelInfoModel.swift */,
				283263DB2D9B8425007465A1 /* UserFeatureConfig.swift */,
				287E94932DE7F04F004EEDFB /* UserDisguiseModel.swift */,
				287E94952DE7FA4E004EEDFB /* UserTitleModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		956795632CFF72D000F03EF8 /* Settings */ = {
			isa = PBXGroup;
			children = (
				283264032D9CEAFC007465A1 /* Model */,
				283263F92D9C1BA9007465A1 /* Manager */,
				283263F02D9C0EDC007465A1 /* Real */,
				283263E92D9BFBA2007465A1 /* Account */,
				956795612CFF72D000F03EF8 /* BlacklistVC.swift */,
				2832640B2D9D1771007465A1 /* AboutUsVC.swift */,
				281B40622E02ACFB00939041 /* CustomConfigVC.swift */,
				2878600B2E0CF46F00367787 /* APIHistorySheetView.swift */,
				281B40602E02AC8C00939041 /* EnvConfigVC.swift */,
				283264002D9CE0FA007465A1 /* DeleteAccountAgreementVC.swift */,
				283264052D9CEB21007465A1 /* DeleteAccountCheckVC.swift */,
				283264072D9CED23007465A1 /* DeleteAccountConfirmVC.swift */,
				283263FC2D9C1DE0007465A1 /* PermissionCell.swift */,
				283263FE2D9CC544007465A1 /* NotificationSettingVC.swift */,
				283263F72D9C1A51007465A1 /* SystemPermissionVC.swift */,
				283263F52D9C1A0F007465A1 /* PrivacySecurityVC.swift */,
				283263EE2D9C08FB007465A1 /* VerifyNewPhoneVC.swift */,
				283263EC2D9C02B9007465A1 /* VerifyOldPhoneVC.swift */,
				282F07002D9E873D000EDE0F /* UserPhoneAuthVC.swift */,
				956795622CFF72D000F03EF8 /* SettingsVC.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		959B2AC12CE840A90047E096 = {
			isa = PBXGroup;
			children = (
				28E21A972D6D6C8B0007ED12 /* ATAuthSDK.bundle */,
				959B2ACC2CE840A90047E096 /* YINDONG */,
				959B2ACB2CE840A90047E096 /* Products */,
				7AB5B925D5A5E504DB552263 /* Pods */,
				2FC1011CED08D77A8577C23E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		959B2ACB2CE840A90047E096 /* Products */ = {
			isa = PBXGroup;
			children = (
				959B2ACA2CE840A90047E096 /* YINDONG.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		959B2ACC2CE840A90047E096 /* YINDONG */ = {
			isa = PBXGroup;
			children = (
				283D57032DF27D80001C4D67 /* YINDONGDebug.entitlements */,
				28E21AB32D6F05390007ED12 /* YINDONG.entitlements */,
				28E21A682D6C66D90007ED12 /* Main */,
				95DAA5A12D2D6CAE00126D4E /* Controllers */,
				95DAA59F2D2D6CAE00126D4E /* Views */,
				9567955F2CFF711F00F03EF8 /* Models */,
				959B2AE12CE842ED0047E096 /* Class */,
				959B2ACD2CE840A90047E096 /* AppDelegate.swift */,
				28E21AB42D6F11BB0007ED12 /* PrivacyInfo.xcprivacy */,
				959B2AD62CE840AA0047E096 /* Assets.xcassets */,
				959B2AD82CE840AA0047E096 /* LaunchScreen.storyboard */,
				959B2ADB2CE840AA0047E096 /* Info.plist */,
			);
			path = YINDONG;
			sourceTree = "<group>";
		};
		959B2AE12CE842ED0047E096 /* Class */ = {
			isa = PBXGroup;
			children = (
				2849F88D2DD31A9F00FA9D53 /* FloatingScreen */,
				283AB3482DB0D10B0086DDB2 /* RoomList */,
				283AB2672DAF863F0086DDB2 /* Room */,
				282F07032DA36F7B000EDE0F /* Report */,
				956795632CFF72D000F03EF8 /* Settings */,
				9567955B2CFF711100F03EF8 /* Auth */,
				959B2B082CE8626F0047E096 /* Mine */,
				959B2B012CE852670047E096 /* Chat */,
				959B2AF62CE84D420047E096 /* Quiz */,
				959B2AED2CE849C60047E096 /* Rrcode */,
				959B2AE72CE846D30047E096 /* YHome */,
				959B2AE22CE842F30047E096 /* Main */,
			);
			path = Class;
			sourceTree = "<group>";
		};
		959B2AE22CE842F30047E096 /* Main */ = {
			isa = PBXGroup;
			children = (
				959B2AEE2CE84AEF0047E096 /* View */,
				959B2AE32CE843010047E096 /* MainTabBarVC.swift */,
				959B2AE52CE844420047E096 /* YinDBaseVC.swift */,
				283263E52D9BE285007465A1 /* LwBaseWebVC.swift */,
				283263E72D9BE704007465A1 /* WebURL.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		959B2AE72CE846D30047E096 /* YHome */ = {
			isa = PBXGroup;
			children = (
				959B2B0F2CE888170047E096 /* Model */,
				959B2AF12CE84B2D0047E096 /* View */,
				959B2B1A2CE8B4630047E096 /* MusicActListVC.swift */,
			);
			path = YHome;
			sourceTree = "<group>";
		};
		959B2AED2CE849C60047E096 /* Rrcode */ = {
			isa = PBXGroup;
			children = (
				959B2AFE2CE852050047E096 /* View */,
				959B2AFB2CE8516E0047E096 /* Manger */,
				959B2AF92CE84E760047E096 /* InstrumentCollectionViewController.swift */,
				959B2B022CE853440047E096 /* RecordingViewController.swift */,
				2828066B2D926B630015FA35 /* RecordingPanelController.swift */,
				959B2B0D2CE8803C0047E096 /* RecordingsViewController.swift */,
			);
			path = Rrcode;
			sourceTree = "<group>";
		};
		959B2AEE2CE84AEF0047E096 /* View */ = {
			isa = PBXGroup;
			children = (
				959B2AEF2CE84AFD0047E096 /* UniversalPopupView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		959B2AF12CE84B2D0047E096 /* View */ = {
			isa = PBXGroup;
			children = (
				959B2B1C2CE8B4740047E096 /* MusicEventPopView.swift */,
				959B2B182CE8B44F0047E096 /* ActivityTableViewCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		959B2AF62CE84D420047E096 /* Quiz */ = {
			isa = PBXGroup;
			children = (
				959B2AF72CE84D6D0047E096 /* MusicTheoryQuizViewController.swift */,
			);
			path = Quiz;
			sourceTree = "<group>";
		};
		959B2AFB2CE8516E0047E096 /* Manger */ = {
			isa = PBXGroup;
			children = (
				959B2AFC2CE851890047E096 /* TCApAudioRecordingManager.swift */,
			);
			path = Manger;
			sourceTree = "<group>";
		};
		959B2AFE2CE852050047E096 /* View */ = {
			isa = PBXGroup;
			children = (
				954188222D05762F0043071D /* MicrophonePositionView.swift */,
				954188232D05762F0043071D /* PublicMessageView.swift */,
				959B2B0B2CE880250047E096 /* RecordingTableViewCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		959B2B012CE852670047E096 /* Chat */ = {
			isa = PBXGroup;
			children = (
				283260052D9A6EB1007465A1 /* Friends */,
				282806AC2D92A14F0015FA35 /* NewChat */,
				28B931C22D7A8F5000DE4F8E /* inputView */,
				9541881A2D055ABF0043071D /* Manager */,
				956795542CFF529300F03EF8 /* Models */,
				956795512CFF529300F03EF8 /* Views */,
				959B2B042CE85D120047E096 /* ChatMainVC.swift */,
				959B2B062CE85D960047E096 /* SyteamListVC.swift */,
				9567954C2CFF528C00F03EF8 /* ChatDetailVC.swift */,
				9567954B2CFF528C00F03EF8 /* ChatListVC.swift */,
			);
			path = Chat;
			sourceTree = "<group>";
		};
		959B2B082CE8626F0047E096 /* Mine */ = {
			isa = PBXGroup;
			children = (
				28785FE72E0A8B3D00367787 /* MainGoods */,
				281B408A2E0A436100939041 /* Model */,
				281B40642E02D36F00939041 /* Mansion */,
				28C1ED0E2DD73E4F006F0442 /* SkillGoods */,
				2849F8962DD3717600FA9D53 /* Skill */,
				282806532D9131990015FA35 /* View */,
				282806492D8C0C0B0015FA35 /* Pearls */,
				282805482D89911B0015FA35 /* UserPage */,
				2840EDEA2D83C8EB000A7EA5 /* EditUser */,
				959B2B092CE862770047E096 /* MineVC.swift */,
				281B40862E0A40B500939041 /* MineUserVC.swift */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		959B2B0F2CE888170047E096 /* Model */ = {
			isa = PBXGroup;
			children = (
				959B2B162CE8B4320047E096 /* MusicEvent.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		95DAA59F2D2D6CAE00126D4E /* Views */ = {
			isa = PBXGroup;
			children = (
				28B932372D7EFCCA00DE4F8E /* DyImagePickerView.swift */,
				95DAA5B62D2D78A500126D4E /* ReportActionSheet.swift */,
				95DAA5AE2D2D731200126D4E /* MusicianCell.swift */,
				95DAA59E2D2D6CAE00126D4E /* PostCell.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		95DAA5A12D2D6CAE00126D4E /* Controllers */ = {
			isa = PBXGroup;
			children = (
				288549062D7541050047A69D /* ViewController */,
				288549012D71B9B00047A69D /* Model */,
				288548F92D71A2CB0047A69D /* View */,
				95DAA5B22D2D750D00126D4E /* UserProfileViewController.swift */,
				95DAA5AC2D2D730A00126D4E /* FeedListViewController.swift */,
				95DAA5AA2D2D730400126D4E /* MusicianListViewController.swift */,
				95DAA5A62D2D70EA00126D4E /* CreatePostViewController.swift */,
				95DAA5A72D2D70EA00126D4E /* PostDetailViewController.swift */,
				95DAA5A02D2D6CAE00126D4E /* SocialFeedViewController.swift */,
				95DAA5A42D2D6CCF00126D4E /* TwoMainViewController.swift */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		959B2AC92CE840A90047E096 /* YINDONG */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 959B2ADE2CE840AA0047E096 /* Build configuration list for PBXNativeTarget "YINDONG" */;
			buildPhases = (
				6C3C92B1CAF3F80F13A1927E /* [CP] Check Pods Manifest.lock */,
				959B2AC62CE840A90047E096 /* Sources */,
				959B2AC72CE840A90047E096 /* Frameworks */,
				959B2AC82CE840A90047E096 /* Resources */,
				789DF6F68C40A7A5788214A4 /* [CP] Embed Pods Frameworks */,
				30878013640E13C61AC8BDF2 /* [CP] Copy Pods Resources */,
				AE725346FB5D426E4B4C419B /* Fix Privacy Manifest */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = YINDONG;
			productName = YINDONG;
			productReference = 959B2ACA2CE840A90047E096 /* YINDONG.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		959B2AC22CE840A90047E096 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1530;
				LastUpgradeCheck = 1530;
				TargetAttributes = {
					959B2AC92CE840A90047E096 = {
						CreatedOnToolsVersion = 15.3;
						LastSwiftMigration = 1620;
					};
				};
			};
			buildConfigurationList = 959B2AC52CE840A90047E096 /* Build configuration list for PBXProject "YINDONG" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 959B2AC12CE840A90047E096;
			productRefGroup = 959B2ACB2CE840A90047E096 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				959B2AC92CE840A90047E096 /* YINDONG */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		959B2AC82CE840A90047E096 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2825A3C82DF7E0DB00EE425C /* DragonHurt.ttf in Resources */,
				289F9E8F2DCB0D7D00D7522D /* boss_max_aida.svga in Resources */,
				2825A3CE2DF8409100EE425C /* AlibabaPuHuiTi_2_115_Black.ttf in Resources */,
				289F9E902DCB0D7D00D7522D /* boss_mini_aida.svga in Resources */,
				289F9E912DCB0D7D00D7522D /* boss_mini_xh.svga in Resources */,
				289F9E922DCB0D7D00D7522D /* boss_max_xh.svga in Resources */,
				959B2AD72CE840AA0047E096 /* Assets.xcassets in Resources */,
				2885494D2D7937E30047A69D /* emoji_mapping.json in Resources */,
				959B2ADA2CE840AA0047E096 /* Base in Resources */,
				288549312D7729A40047A69D /* le_city.json in Resources */,
				281C6FD52DFFFF43002F9D99 /* skill_talk_success.json in Resources */,
				281C6FD62DFFFF43002F9D99 /* skill_hole_fail.json in Resources */,
				281C6FD72DFFFF43002F9D99 /* skill_catch_fail.json in Resources */,
				281C6FD82DFFFF43002F9D99 /* skill_kiss_fail.json in Resources */,
				281C6FD92DFFFF43002F9D99 /* skill_kiss_success.json in Resources */,
				281C6FDA2DFFFF43002F9D99 /* skill_hole_success.json in Resources */,
				281C6FDB2DFFFF43002F9D99 /* skill_foot_fail.json in Resources */,
				281B40532E018FC400939041 /* skill_level_55.lottie in Resources */,
				281B40542E018FC400939041 /* skill_level_65.lottie in Resources */,
				281B40552E018FC400939041 /* skill_level_80.lottie in Resources */,
				281B40562E018FC400939041 /* skill_level_85.lottie in Resources */,
				281B40572E018FC400939041 /* skill_level_90.lottie in Resources */,
				281B40582E018FC400939041 /* skill_level_70.lottie in Resources */,
				281B40592E018FC400939041 /* skill_level_50.lottie in Resources */,
				281B405A2E018FC400939041 /* skill_level_75.lottie in Resources */,
				281B405B2E018FC400939041 /* skill_level_60.lottie in Resources */,
				281B405C2E018FC400939041 /* skill_level_45.lottie in Resources */,
				281B405D2E018FC400939041 /* skill_level_95.lottie in Resources */,
				281C6FDC2DFFFF43002F9D99 /* skill_foot_success.json in Resources */,
				281C6FDD2DFFFF43002F9D99 /* skill_talk_fail.json in Resources */,
				281B40682E02DA1400939041 /* work_new_select.svga in Resources */,
				281C6FDE2DFFFF43002F9D99 /* skill_catch_success.json in Resources */,
				283D57062DF28DA6001C4D67 /* login_new.zip in Resources */,
				28E21AB52D6F11BB0007ED12 /* PrivacyInfo.xcprivacy in Resources */,
				28E21A982D6D6C8B0007ED12 /* ATAuthSDK.bundle in Resources */,
				289F9E9E2DCC929200D7522D /* gaojiMofa.mp4 in Resources */,
				2885493B2D7829180047A69D /* loading2.json in Resources */,
				289F9EA82DCCAB6600D7522D /* putongMF.mp4 in Resources */,
				289F9EA62DCC9F4200D7522D /* boss_box.zip in Resources */,
				289F9EAB2DCCAC9F00D7522D /* boss_attPath.svga in Resources */,
				2825A3C32DF6D14700EE425C /* dragon_enter.mp4 in Resources */,
				2825A3C42DF6D14700EE425C /* dragon_normal.mp4 in Resources */,
				289F9EAC2DCCAC9F00D7522D /* boss_boom.svga in Resources */,
				289F9E9C2DCC5C6400D7522D /* boss_fazhen.svga in Resources */,
				288549452D7843330047A69D /* dy_voicePlay.svga in Resources */,
				283260122D9A7E8E007465A1 /* numFont.ttf in Resources */,
				281B40442E013D6100939041 /* skill_level_100.lottie in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		30878013640E13C61AC8BDF2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YINDONG/Pods-YINDONG-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YINDONG/Pods-YINDONG-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-YINDONG/Pods-YINDONG-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6C3C92B1CAF3F80F13A1927E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-YINDONG-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		789DF6F68C40A7A5788214A4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YINDONG/Pods-YINDONG-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YINDONG/Pods-YINDONG-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-YINDONG/Pods-YINDONG-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AE725346FB5D426E4B4C419B /* Fix Privacy Manifest */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Fix Privacy Manifest";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 1;
			shellPath = /bin/sh;
			shellScript = "\"/Users/<USER>/Downloads/app_privacy_manifest_fixer/fixer.sh\" ";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		959B2AC62CE840A90047E096 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2832600C2D9A6EE8007465A1 /* FriendBaseCell.swift in Sources */,
				289F9E042DC9F7D300D7522D /* RoomToolSheetView.swift in Sources */,
				2832640C2D9D1771007465A1 /* AboutUsVC.swift in Sources */,
				28E21A712D6C6AF30007ED12 /* ApiResponse.swift in Sources */,
				28E21AB22D6EF1AD0007ED12 /* FeedbackVC.swift in Sources */,
				959B2AE62CE844420047E096 /* YinDBaseVC.swift in Sources */,
				2878600C2E0CF46F00367787 /* APIHistorySheetView.swift in Sources */,
				282806CA2D92D77F0015FA35 /* GiftModel.swift in Sources */,
				288548F82D719D9F0047A69D /* BaseBannerView.swift in Sources */,
				95DAA5A92D2D70EA00126D4E /* PostDetailViewController.swift in Sources */,
				283260032D9A3A0B007465A1 /* LwChatMainVC.swift in Sources */,
				283263E82D9BE704007465A1 /* WebURL.swift in Sources */,
				289F9EAE2DCCD3B600D7522D /* BossBuyBookSheetView.swift in Sources */,
				954188182D0558990043071D /* MessageStorage.swift in Sources */,
				283AB1A02DAD048F0086DDB2 /* MP4AnimationPlayer.swift in Sources */,
				959B2B0A2CE862770047E096 /* MineVC.swift in Sources */,
				282806592D9138910015FA35 /* UserLevelInfoModel.swift in Sources */,
				2825A3FE2DFAB40900EE425C /* ComboButton.swift in Sources */,
				282806C52D92B9D80015FA35 /* MsgBaseModel.swift in Sources */,
				2885491C2D759C750047A69D /* TagLabelView.swift in Sources */,
				9567954D2CFF528C00F03EF8 /* ChatListVC.swift in Sources */,
				2885491E2D7697480047A69D /* DynamicDetailVC.swift in Sources */,
				28AFCE822DBA2D56003CE291 /* RoomSeatConfiguration.swift in Sources */,
				287E94172DE5931D004EEDFB /* CpManeuverCell.swift in Sources */,
				************************ /* PlayerManager.swift in Sources */,
				2825A3D32DF8571A00EE425C /* TuLongRecordModel.swift in Sources */,
				28E21A752D6C6F530007ED12 /* ApiService.swift in Sources */,
				283260072D9A6EC3007465A1 /* LwFriendsMainVC.swift in Sources */,
				281B40812E05583900939041 /* RuneDisplayView.swift in Sources */,
				287860042E0BF63700367787 /* MyPackMainVC.swift in Sources */,
				28B9323A2D801D8900DE4F8E /* RateLimitManager.swift in Sources */,
				28AFCDAD2DB5F740003CE291 /* RoomSeatView.swift in Sources */,
				28EB87EF2DF0440000D389FB /* HotInfoPopupView.swift in Sources */,
				282805962D8A63EF0015FA35 /* BasePageInfoView.swift in Sources */,
				282806C22D92B92B0015FA35 /* NoticeMsgCell.swift in Sources */,
				28F643E92D953B0700487E43 /* AudioRecordingProgressView.swift in Sources */,
				2849F8A62DD48F3900FA9D53 /* RoomHtmlMsgCell.swift in Sources */,
				288549182D75954F0047A69D /* DynamicBaseListVC.swift in Sources */,
				2849F8B02DD5E0EC00FA9D53 /* LuZiPaySheetView.swift in Sources */,
				287E941B2DE5B20D004EEDFB /* SyTextLinkCell.swift in Sources */,
				287E94992DE8423F004EEDFB /* GiftUserListBar.swift in Sources */,
				28BB74A42DD1D348004B9997 /* JingLiCell.swift in Sources */,
				288549432D783DD80047A69D /* SVGAnimationPlayer.swift in Sources */,
				2849F89A2DD3719700FA9D53 /* SkillListVC.swift in Sources */,
				956795552CFF529300F03EF8 /* ChatListCell.swift in Sources */,
				2840EDEC2D83C8FC000A7EA5 /* EditMainVC.swift in Sources */,
				287E94062DE45DA2004EEDFB /* RoomSongMainView.swift in Sources */,
				959B2B052CE85D120047E096 /* ChatMainVC.swift in Sources */,
				28AFCFDB2DC0CD91003CE291 /* RoomDetailSheetView.swift in Sources */,
				288549482D7846FD0047A69D /* MediaPlaybackManager.swift in Sources */,
				2849F8982DD3718700FA9D53 /* SkillMainListVC.swift in Sources */,
				287E941D2DE5BCA7004EEDFB /* InvterFansPopView.swift in Sources */,
				283264682D9E25A5007465A1 /* GiftPanelView.swift in Sources */,
				2825A3E72DF92DC700EE425C /* TuLongRoomRankItemVC.swift in Sources */,
				288549352D77EE780047A69D /* FollowNodataView.swift in Sources */,
				28AFCDB42DB654C2003CE291 /* SideMenuView.swift in Sources */,
				28BB74A82DD1ED0F004B9997 /* BossGoodsdhSheetView.swift in Sources */,
				28E21AA92D6DD7100007ED12 /* SendCodeVC.swift in Sources */,
				283263F42D9C0FD5007465A1 /* RealNameAuthSuccessView.swift in Sources */,
				283264042D9CEAFC007465A1 /* DeleteAccountCheckModel.swift in Sources */,
				28AFCDBB2DB72430003CE291 /* AudioWaveView.swift in Sources */,
				28EAA6B42D9E7CC0001600E8 /* MatchQuestionModalView.swift in Sources */,
				2840EDF82D840A77000A7EA5 /* BaseEditProfileCell.swift in Sources */,
				281B40902E0A43CC00939041 /* UserHeaderView.swift in Sources */,
				281B40912E0A43CC00939041 /* ServiceMenuView.swift in Sources */,
				281B40922E0A43CC00939041 /* FunctionGridView.swift in Sources */,
				282804E02D8832150015FA35 /* TagBaseMainVC.swift in Sources */,
				2849F8A82DD597CB00FA9D53 /* SkillLevelRatingView.swift in Sources */,
				2849F8B42DD5F1E700FA9D53 /* UserYuanbaoVC.swift in Sources */,
				954188252D05762F0043071D /* PublicMessageView.swift in Sources */,
				28E21AA22D6DA0AC0007ED12 /* AliyunOssHelper.swift in Sources */,
				28AFCDBF2DB730C3003CE291 /* RoomTypeCell.swift in Sources */,
				28B932362D7ED83100DE4F8E /* DySelUserCell.swift in Sources */,
				95DAA5AB2D2D730400126D4E /* MusicianListViewController.swift in Sources */,
				28AFCE862DBA45A2003CE291 /* RoomMessageCell.swift in Sources */,
				95DAA5B52D2D789600126D4E /* ReportModel.swift in Sources */,
				283AB3522DB0D9360086DDB2 /* RoomInfoModel.swift in Sources */,
				28AFCDC32DB73AA4003CE291 /* GameTypeCell.swift in Sources */,
				954188202D0561F90043071D /* ChatUserManager.swift in Sources */,
				28E21A772D6C706B0007ED12 /* NetworkUtility.swift in Sources */,
				28553F502D87C3710042C619 /* CommonAlignFlowLayout.swift in Sources */,
				28F643F12D967DBC00487E43 /* TacitOptionCardSheetView.swift in Sources */,
				28F643ED2D95656D00487E43 /* TacitOptionCell.swift in Sources */,
				2825A3B82DF6B66C00EE425C /* TuLongMainSheetView.swift in Sources */,
				28F643EF2D95664800487E43 /* TacitModel.swift in Sources */,
				288548FB2D71A2DB0047A69D /* DyHeaderView.swift in Sources */,
				28F643412D93F8D200487E43 /* ChatDetailUserModel.swift in Sources */,
				2866EB802DC1D743005B0D13 /* RoomUserMainSheetV.swift in Sources */,
				2825A3B62DF6788F00EE425C /* RedPacketRainView.swift in Sources */,
				287E94012DE3FF0D004EEDFB /* RoomBgListVC.swift in Sources */,
				2840EDA22D82E68D000A7EA5 /* DynamicNoticeVC.swift in Sources */,
				2825A3DD2DF91B2D00EE425C /* TuLongRankModel.swift in Sources */,
				283264062D9CEB21007465A1 /* DeleteAccountCheckVC.swift in Sources */,
				288548EB2D7002530047A69D /* LWUserModel.swift in Sources */,
				283263FB2D9C1BB5007465A1 /* PermissionManager.swift in Sources */,
				959B2B1B2CE8B4630047E096 /* MusicActListVC.swift in Sources */,
				28E785332DC9BA590092CED9 /* RoomSummaryVC.swift in Sources */,
				28785FFB2E0B887200367787 /* ShowSpecialView.swift in Sources */,
				283AB34A2DB0D1290086DDB2 /* HomeRoomListVC.swift in Sources */,
				283260202D9AA3EE007465A1 /* SyteamModel.swift in Sources */,
				28785FF12E0A9A1600367787 /* DecorationMainVC.swift in Sources */,
				288549282D76FA030047A69D /* String_Ext.swift in Sources */,
				289F9E892DCB0C9000D7522D /* BossAttackFloatingView.swift in Sources */,
				2885499E2D79BB6D0047A69D /* EmojiTextView.swift in Sources */,
				283264602D9E22AA007465A1 /* GiftCollectionVC.swift in Sources */,
				287E93F92DE06F1A004EEDFB /* GiftQueManager.swift in Sources */,
				28AFCDC52DB791E5003CE291 /* AuthenticationPopupView.swift in Sources */,
				28C1ED1C2DDADF9E006F0442 /* GoodsBuyPopView.swift in Sources */,
				283AB34F2DB0D5380086DDB2 /* RoomManger.swift in Sources */,
				959B2B172CE8B4320047E096 /* MusicEvent.swift in Sources */,
				282F07012D9E873D000EDE0F /* UserPhoneAuthVC.swift in Sources */,
				285DA64C2DFAF02900BDCDAD /* BossRedRedBegainV.swift in Sources */,
				283263E62D9BE285007465A1 /* LwBaseWebVC.swift in Sources */,
				28785FFD2E0BA49400367787 /* BaseChatBgView.swift in Sources */,
				282F07062DA3B86B000EDE0F /* InteractListVC.swift in Sources */,
				287E948C2DE5D717004EEDFB /* RoomInvterUserSheetView.swift in Sources */,
				283AB1972DACF2700086DDB2 /* SVGAAnimationPlayer.swift in Sources */,
				28AFCE792DBA123A003CE291 /* RoomViewModel.swift in Sources */,
				2825A4012DFABE3900EE425C /* RedBagBaseModel.swift in Sources */,
				283263DC2D9B8425007465A1 /* UserFeatureConfig.swift in Sources */,
				2825A3F22DF96DEC00EE425C /* TulongSettlementPopView.swift in Sources */,
				282F07132DA67767000EDE0F /* ChatHelp.swift in Sources */,
				288548F32D7073D80047A69D /* ThirdBindPhoneVC.swift in Sources */,
				959B2B192CE8B44F0047E096 /* ActivityTableViewCell.swift in Sources */,
				28AFCDB62DB65ABE003CE291 /* RoomInfoTopView.swift in Sources */,
				283AB1952DACEF260086DDB2 /* PNGAnimationPlayer.swift in Sources */,
				283263ED2D9C02B9007465A1 /* VerifyOldPhoneVC.swift in Sources */,
				287E94082DE463B8004EEDFB /* RoomSongListVC.swift in Sources */,
				287E94922DE70196004EEDFB /* RoomChatInputPopupView.swift in Sources */,
				2825A3CA2DF835C800EE425C /* TuLongRedLuckPopView.swift in Sources */,
				28EB87E92DEED09200D389FB /* RoomEditTitleView.swift in Sources */,
				2849F8952DD3539600FA9D53 /* BossAttackManager.swift in Sources */,
				288549332D77EB330047A69D /* DynamicFollowVC.swift in Sources */,
				9567955C2CFF711100F03EF8 /* LoginVC.swift in Sources */,
				287E949B2DE87AEA004EEDFB /* BossTiaoJIanSheetView.swift in Sources */,
				283AB19C2DACFF680086DDB2 /* LSZipLottieFileManager.swift in Sources */,
				959B2ACE2CE840A90047E096 /* AppDelegate.swift in Sources */,
				959B2B002CE8520C0047E096 /* AudioRecordingHUDView.swift in Sources */,
				287E94962DE7FA4E004EEDFB /* UserTitleModel.swift in Sources */,
				288548F12D7019EF0047A69D /* PasswordLoginVC.swift in Sources */,
				28785FEC2E0A91FB00367787 /* DressUpListCell.swift in Sources */,
				287E94132DE55F79004EEDFB /* RoomItemView.swift in Sources */,
				28E21AAC2D6EB1930007ED12 /* LoginUserModel.swift in Sources */,
				2825A3FA2DFA844700EE425C /* ExitRoomTaskPopView.swift in Sources */,
				28BB74A62DD1E925004B9997 /* BossMoreTextView.swift in Sources */,
				283AB1A22DAD09500086DDB2 /* FileManagerWrapper.swift in Sources */,
				281B406C2E02E1FC00939041 /* MansionMainModel.swift in Sources */,
				28EB87ED2DF0214200D389FB /* RoomSeatDegreeView.swift in Sources */,
				9567954E2CFF528C00F03EF8 /* ChatDetailVC.swift in Sources */,
				28AFCDB92DB66717003CE291 /* RoomDetailModel.swift in Sources */,
				282F070E2DA61022000EDE0F /* FriendRemarkVC.swift in Sources */,
				282804DB2D880C540015FA35 /* EditTagSeletedSheetView.swift in Sources */,
				959B2AF82CE84D6D0047E096 /* MusicTheoryQuizViewController.swift in Sources */,
				281B40852E09614D00939041 /* TuLongRewardWithNameCell.swift in Sources */,
				28B932942D81367B00DE4F8E /* LWJumpManger.swift in Sources */,
				2885493D2D782AF80047A69D /* LWLoadingView.swift in Sources */,
				2825A3E12DF91B7E00EE425C /* TuLongRewardCell.swift in Sources */,
				28785FEF2E0A970800367787 /* DressBaseModel.swift in Sources */,
				2825A3E22DF91B7E00EE425C /* TuLongRankCell.swift in Sources */,
				2825A3E32DF91B7E00EE425C /* TuLongMyRankBarView.swift in Sources */,
				283263F62D9C1A0F007465A1 /* PrivacySecurityVC.swift in Sources */,
				2832646C2D9E2848007465A1 /* GiftQuantitySelectionView.swift in Sources */,
				289F9EA02DCC98E100D7522D /* CustomProgressBar.swift in Sources */,
				2825A3D72DF8572D00EE425C /* TuLongRecordEmptyView.swift in Sources */,
				2885494B2D787A490047A69D /* DyInputView.swift in Sources */,
				954188242D05762F0043071D /* MicrophonePositionView.swift in Sources */,
				283AB1932DACEEBF0086DDB2 /* AnimationPlayer.swift in Sources */,
				959B2B032CE853440047E096 /* RecordingViewController.swift in Sources */,
				281B40832E08E10B00939041 /* RuneDisplayInfoListView.swift in Sources */,
				283AB19E2DAD031C0086DDB2 /* VapAnimationPlayer.swift in Sources */,
				282F070C2DA60C05000EDE0F /* ChatWarningView.swift in Sources */,
				28AFCEF42DBB6882003CE291 /* RoomNavUserView.swift in Sources */,
				2849F8AA2DD5BD1A00FA9D53 /* LottieAnimationManager.swift in Sources */,
				2849F89F2DD43A9000FA9D53 /* UpgradeCostView.swift in Sources */,
				282805B22D8AA1460015FA35 /* UserTagCardView.swift in Sources */,
				282806CE2D93D1580015FA35 /* ChatCardCell.swift in Sources */,
				2840EE002D840BC6000A7EA5 /* EditTagListCell.swift in Sources */,
				283260182D9A9A48007465A1 /* HobbyModel.swift in Sources */,
				288549542D793C370047A69D /* EmojiKeyboardVC.swift in Sources */,
				288549262D76E0F10047A69D /* CommentOneCell.swift in Sources */,
				2840EDF42D840A6A000A7EA5 /* EditProfileModel.swift in Sources */,
				288549522D7938530047A69D /* EmojiManger.swift in Sources */,
				28B932D72D81602000DE4F8E /* MessagePopupView.swift in Sources */,
				28AFCE032DB7B776003CE291 /* RoomBgView.swift in Sources */,
				287860082E0C0F8E00367787 /* WearPrePopView.swift in Sources */,
				285DA6482DFAC1EB00BDCDAD /* RedActView.swift in Sources */,
				959B2B0C2CE880250047E096 /* RecordingTableViewCell.swift in Sources */,
				95DAA5A82D2D70EA00126D4E /* CreatePostViewController.swift in Sources */,
				2832600A2D9A6ED3007465A1 /* LwFriendsListVC.swift in Sources */,
				282805502D8998920015FA35 /* UserCardListVC.swift in Sources */,
				289F9F6B2DCDEE6000D7522D /* StackedNotificationView.swift in Sources */,
				28E21A6F2D6C687F0007ED12 /* AppTool.swift in Sources */,
				282806BC2D92B8C00015FA35 /* MsgBaseCell.swift in Sources */,
				287E93562DDEF71D004EEDFB /* RoomAdminListVC.swift in Sources */,
				95DAA5AD2D2D730A00126D4E /* FeedListViewController.swift in Sources */,
				2885490A2D7541280047A69D /* TopicDetailVC.swift in Sources */,
				28AFCE052DB88728003CE291 /* AvatarFrameView.swift in Sources */,
				28AFCF982DBE3AA2003CE291 /* RoomInfoCardSheetView.swift in Sources */,
				28AFCFCC2DBF8B49003CE291 /* SeatActionManager.swift in Sources */,
				28E785312DC9AD440092CED9 /* AudienceRoomClosedVC.swift in Sources */,
				288549222D76D9BD0047A69D /* RemarkTopCell.swift in Sources */,
				288549372D77FAC20047A69D /* DynamicNewListVC.swift in Sources */,
				2849F8A42DD46E9D00FA9D53 /* SkillItemModel.swift in Sources */,
				287E94032DE43C9E004EEDFB /* RoomGiftOpenSheetView.swift in Sources */,
				2832601A2D9A9FDC007465A1 /* UserGiftListVC.swift in Sources */,
				2832601C2D9AA206007465A1 /* NotificationPromptView.swift in Sources */,
				283AB1A42DAD0B190086DDB2 /* AnimationPlayView.swift in Sources */,
				283260142D9A97C4007465A1 /* HobbyListVC.swift in Sources */,
				28C1ED122DD73E71006F0442 /* SkillGoodsListVC.swift in Sources */,
				281B40462E01838500939041 /* PkResultPopView.swift in Sources */,
				2825A3F82DFA650400EE425C /* TaskSurePopView.swift in Sources */,
				28785FF92E0AC85F00367787 /* DressBuyPopView.swift in Sources */,
				2821891F2E0E8E6200362550 /* ZuoJiaMangement.swift in Sources */,
				283263E02D9B98B5007465A1 /* ChatStatusPickerView.swift in Sources */,
				95DAA5A32D2D6CAE00126D4E /* SocialFeedViewController.swift in Sources */,
				282F07042DA36F7B000EDE0F /* ReportViewController.swift in Sources */,
				2825A3F62DF99A1D00EE425C /* TaskCell.swift in Sources */,
				281B40782E03E9DC00939041 /* MansionTaskSheetView.swift in Sources */,
				28C1ED1F2DDB2E1E006F0442 /* RoomTextMsgCell.swift in Sources */,
				28B931C42D7A8F6000DE4F8E /* ChatInputView.swift in Sources */,
				2825A3FC2DFAAB6E00EE425C /* AdvertView.swift in Sources */,
				28785FE62E0A7D9900367787 /* UserInfoCollectionModel.swift in Sources */,
				289F9E3C2DCA02BD00D7522D /* VoiceRoomActivitySheetView.swift in Sources */,
				2825A3E52DF9281800EE425C /* TuLongRoomRankListVC.swift in Sources */,
				28E21A962D6D6C1B0007ED12 /* QuickManger.swift in Sources */,
				28C1ED0D2DD72CF6006F0442 /* UpgradeConditionView.swift in Sources */,
				283AB34C2DB0D1890086DDB2 /* HomeRoomBaseVC.swift in Sources */,
				282806B92D92B29C0015FA35 /* MessageHandlerProtocol.swift in Sources */,
				28AFCD5C2DB120C2003CE291 /* HotRoomMainVC.swift in Sources */,
				282806C02D92B8F60015FA35 /* ImageMsgCell.swift in Sources */,
				283263EF2D9C08FB007465A1 /* VerifyNewPhoneVC.swift in Sources */,
				282805472D8984620015FA35 /* EditAdressSheetView.swift in Sources */,
				282805B02D8A9B220015FA35 /* UserCardPhotoView.swift in Sources */,
				28E21A9F2D6D9E510007ED12 /* ThirdQuickView.swift in Sources */,
				2885491A2D7598760047A69D /* ImageCardView.swift in Sources */,
				95DAA5B12D2D732300126D4E /* MusicianModel.swift in Sources */,
				282806552D91319F0015FA35 /* RechargeCardView.swift in Sources */,
				28785FFF2E0BDB5300367787 /* EventPreviewShowView.swift in Sources */,
				2828054C2D8994C70015FA35 /* UserPageMainVC.swift in Sources */,
				287E948E2DE6B123004EEDFB /* RoomLockPopView.swift in Sources */,
				28AFCF3C2DBB9353003CE291 /* RoomChatBarView.swift in Sources */,
				2840EDFC2D840B7C000A7EA5 /* EditTextInfoCell.swift in Sources */,
				288549122D75909D0047A69D /* DynamicBaseCell.swift in Sources */,
				2878600A2E0C1D7B00367787 /* RingBuyView.swift in Sources */,
				283264632D9E22CE007465A1 /* GiftBaseModel.swift in Sources */,
				285DA6542DFC1D1A00BDCDAD /* SkillInfoModel.swift in Sources */,
				283264082D9CED23007465A1 /* DeleteAccountConfirmVC.swift in Sources */,
				287E94152DE56DD7004EEDFB /* CpListSyVC.swift in Sources */,
				287E940B2DE46823004EEDFB /* MusicPlaybackManager.swift in Sources */,
				287E93FD2DE06F78004EEDFB /* GiftShowView.swift in Sources */,
				954188272D0579200043071D /* WebViewController.swift in Sources */,
				287E93FB2DE06F4A004EEDFB /* QueGift.swift in Sources */,
				2825A3DB2DF862E500EE425C /* TuLongBaseAttRankListVC.swift in Sources */,
				9541881D2D055B530043071D /* MessageCell.swift in Sources */,
				282806BE2D92B8D30015FA35 /* TextMsgCell.swift in Sources */,
				285DA64E2DFAFD9600BDCDAD /* RedLuckUserPopV.swift in Sources */,
				288549002D71B8EB0047A69D /* TopicCell.swift in Sources */,
				283264012D9CE0FA007465A1 /* DeleteAccountAgreementVC.swift in Sources */,
				956795602CFF711F00F03EF8 /* UserModel.swift in Sources */,
				************************ /* InvterRoomCell.swift in Sources */,
				28BB74A02DD1CDCF004B9997 /* BossJpListVC.swift in Sources */,
				288549392D77FB380047A69D /* DynamicLocListVC.swift in Sources */,
				2832601E2D9AA3BB007465A1 /* SyteamCell.swift in Sources */,
				283AB3562DB0DAAA0086DDB2 /* RoomSmallCell.swift in Sources */,
				283AB3572DB0DAAA0086DDB2 /* RoomBigCell.swift in Sources */,
				28BB74AC2DD1FDDF004B9997 /* BossMyRateView.swift in Sources */,
				287E94942DE7F04F004EEDFB /* UserDisguiseModel.swift in Sources */,
				2840EDFA2D840B54000A7EA5 /* EditPhotoCell.swift in Sources */,
				2849F8912DD31CD000FA9D53 /* BossChestStatusView.swift in Sources */,
				95DAA5B72D2D78A500126D4E /* ReportActionSheet.swift in Sources */,
				287E940D2DE4722D004EEDFB /* MusicPlayView.swift in Sources */,
				282806B42D92A3EE0015FA35 /* LwSessionModel.swift in Sources */,
				2840EDF22D83CF10000A7EA5 /* UserInfoModel.swift in Sources */,
				282188922E0E2F7400362550 /* ScreenManagement.swift in Sources */,
				289F9E972DCC59F000D7522D /* BossRecordTickerView.swift in Sources */,
				2825A3D92DF8617F00EE425C /* TuLongAttMainSheetV.swift in Sources */,
				28E21AA72D6DC1470007ED12 /* QuickLoginView.swift in Sources */,
				28785FE92E0A8B4B00367787 /* MainGoodsVC.swift in Sources */,
				28E21AAE2D6EB5C00007ED12 /* SelectAccountVC.swift in Sources */,
				28AFCF472DBDC96E003CE291 /* CircleProgressView.swift in Sources */,
				2825A3EB2DF9527F00EE425C /* UniversalTickerView.swift in Sources */,
				2849F88F2DD31AD300FA9D53 /* FloatingScreenView.swift in Sources */,
				282804E22D8832440015FA35 /* TagOptionListVC.swift in Sources */,
				28AFCDAF2DB5F790003CE291 /* RoomContainerView.swift in Sources */,
				2825A3CC2DF8397000EE425C /* CenteredCollectionViewFlowLayout.swift in Sources */,
				2825A3D52DF8572500EE425C /* TuLongRecordCell.swift in Sources */,
				288549412D783A390047A69D /* VoiceBaseCardView.swift in Sources */,
				288548F62D718CE70047A69D /* InfoItemBaseView.swift in Sources */,
				28EB87F22DF1383D00D389FB /* VolumePopupView.swift in Sources */,
				28C1ED0B2DD72CE1006F0442 /* TuPoView.swift in Sources */,
				2832646A2D9E26D1007465A1 /* GiftPanelFooterView.swift in Sources */,
				287E94902DE6F0D6004EEDFB /* RoomSettingSheetView.swift in Sources */,
				28E21A9D2D6D9B550007ED12 /* Notification_Ext.swift in Sources */,
				282805B42D8AD4BC0015FA35 /* UserPageBottomView.swift in Sources */,
				959B2AFD2CE851890047E096 /* TCApAudioRecordingManager.swift in Sources */,
				2832640F2D9D50F7007465A1 /* VoiceCell.swift in Sources */,
				288548E92D6FF8260047A69D /* T_IMHelper.swift in Sources */,
				9541881B2D055ABF0043071D /* ChatAudioManager.swift in Sources */,
				959B2B0E2CE8803C0047E096 /* RecordingsViewController.swift in Sources */,
				2885490E2D7554B80047A69D /* TopicSelListVC.swift in Sources */,
				2825A3BB2DF6B75B00EE425C /* TaskCarousel.swift in Sources */,
				28AFCE842DBA4395003CE291 /* RoomMsgType.swift in Sources */,
				283263E42D9BC3C9007465A1 /* AddFreiendListVC.swift in Sources */,
				282F070A2DA4B587000EDE0F /* UserListDynamicVC.swift in Sources */,
				2828066C2D926B630015FA35 /* RecordingPanelController.swift in Sources */,
				281B407E2E050EB800939041 /* MansionHisPopView.swift in Sources */,
				281B407A2E042FC500939041 /* MansionMangerListVC.swift in Sources */,
				281B40722E02E94B00939041 /* MansionAttendantSectionView.swift in Sources */,
				281B40732E02E94B00939041 /* MansionHeaderInfoView.swift in Sources */,
				28785FF72E0ABC5C00367787 /* HotGoodsListVC.swift in Sources */,
				281B40742E02E94B00939041 /* MansionMasterSectionView.swift in Sources */,
				281B40752E02E94B00939041 /* MansionWorkSectionView.swift in Sources */,
				281B40762E02E94B00939041 /* MansionAttendantCell.swift in Sources */,
				2840EE042D8431BD000A7EA5 /* EditUserTextSheetView.swift in Sources */,
				2885493F2D782C520047A69D /* LodingRefeshHeaderView.swift in Sources */,
				289F9E9A2DCC5A4100D7522D /* BossRecordRotation.swift in Sources */,
				28F643DD2D9535F700487E43 /* SendMsg-Ext.swift in Sources */,
				28E21A6C2D6C68280007ED12 /* EnvManager.swift in Sources */,
				2825A3D02DF852D500EE425C /* TuLongRecordSheetView.swift in Sources */,
				28E21AA42D6DAC030007ED12 /* ProfileSetupVC.swift in Sources */,
				28AFCE072DB8DE71003CE291 /* RoomMsgManger.swift in Sources */,
				956795572CFF529300F03EF8 /* ConversationModel.swift in Sources */,
				282F07082DA4B45B000EDE0F /* UserDynamicCell.swift in Sources */,
				28785FF32E0A9A2800367787 /* DecorationListVC.swift in Sources */,
				959B2B1D2CE8B4740047E096 /* MusicEventPopView.swift in Sources */,
				28B932382D7EFCCA00DE4F8E /* DyImagePickerView.swift in Sources */,
				281B408B2E0A436100939041 /* MenuItemManager.swift in Sources */,
				281B408C2E0A436100939041 /* MenuItemModel.swift in Sources */,
				283263FD2D9C1DE0007465A1 /* PermissionCell.swift in Sources */,
				28AFCE012DB7A7BF003CE291 /* RoomFloatingWindowManager.swift in Sources */,
				95DAA5A52D2D6CCF00126D4E /* TwoMainViewController.swift in Sources */,
				2828064C2D8C12430015FA35 /* PearlsModel.swift in Sources */,
				281C6FC92DFFFB3D002F9D99 /* UseSkillCell.swift in Sources */,
				283263FF2D9CC544007465A1 /* NotificationSettingVC.swift in Sources */,
				287E93582DDEF728004EEDFB /* RoomAdminAddVC.swift in Sources */,
				28B931C62D7ACD9900DE4F8E /* DyPostInputView.swift in Sources */,
				288549242D76E04B0047A69D /* CommentModel.swift in Sources */,
				2840B2862D828629007C907C /* GenericActionSheetView.swift in Sources */,
				283263EB2D9BFBB9007465A1 /* AccountPhoneVC.swift in Sources */,
				2885490C2D75522C0047A69D /* CreatePostVC.swift in Sources */,
				282805AE2D8A99130015FA35 /* UserCardInfoView.swift in Sources */,
				2840EE022D841CDD000A7EA5 /* EditProfileDataProvider.swift in Sources */,
				283263F22D9C0F98007465A1 /* RealNameAuthVC.swift in Sources */,
				28BB74A22DD1D31F004B9997 /* JiangPingCell.swift in Sources */,
				288549102D755D8E0047A69D /* ImageExpandingView.swift in Sources */,
				287E93FF2DE06FA8004EEDFB /* GiftNumView.swift in Sources */,
				956795582CFF529300F03EF8 /* MessageModel.swift in Sources */,
				28C1ED1A2DDACDF0006F0442 /* GoodsCell.swift in Sources */,
				289F9E3A2DCA027D00D7522D /* VoiceRoomActivityCell.swift in Sources */,
				28AFCE7D2DBA12A3003CE291 /* RoomBaseMsgModel.swift in Sources */,
				28EB87EB2DEFEB8000D389FB /* RoomTypeSelectionView.swift in Sources */,
				288548EF2D70065A0047A69D /* WaitLuanchVC.swift in Sources */,
				283260162D9A9932007465A1 /* HobbyUserCell.swift in Sources */,
				2825A3C02DF6BFD100EE425C /* TLCell.swift in Sources */,
				956795642CFF72D100F03EF8 /* BlacklistVC.swift in Sources */,
				959B2AFA2CE84E760047E096 /* InstrumentCollectionViewController.swift in Sources */,
				959B2AF02CE84AFD0047E096 /* UniversalPopupView.swift in Sources */,
				2825A3F42DF9721000EE425C /* TuLongSettlementCell.swift in Sources */,
				28BB74AA2DD1F7F1004B9997 /* BossGoodsCell.swift in Sources */,
				95DAA5A22D2D6CAE00126D4E /* PostCell.swift in Sources */,
				2849F89D2DD4392400FA9D53 /* LipPowerView.swift in Sources */,
				28B932342D7ED6F900DE4F8E /* DySelUserlistVC.swift in Sources */,
				959B2AE42CE843010047E096 /* MainTabBarVC.swift in Sources */,
				281B40612E02AC8C00939041 /* EnvConfigVC.swift in Sources */,
				28C1ED102DD73E68006F0442 /* SkillGoodsMainVC.swift in Sources */,
				283AB1992DACFCDE0086DDB2 /* LottieAnimationPlayer.swift in Sources */,
				2840EDA42D82F248000A7EA5 /* DynoticeModel.swift in Sources */,
				956795652CFF72D100F03EF8 /* SettingsVC.swift in Sources */,
				282F07102DA6206A000EDE0F /* IntimacyTipPopView.swift in Sources */,
				28AFCF9D2DBF34CB003CE291 /* AppConfigManger.swift in Sources */,
				283263F82D9C1A51007465A1 /* SystemPermissionVC.swift in Sources */,
				2840EDFE2D840BAA000A7EA5 /* EditUserAlbumCell.swift in Sources */,
				28AFCE7B2DBA127D003CE291 /* RoomPublicMessageVC.swift in Sources */,
				281B407C2E043C3800939041 /* MansionSytemPopView.swift in Sources */,
				2885492D2D77248C0047A69D /* LocationManager.swift in Sources */,
				282806B72D92B1670015FA35 /* LwChatDetailVC.swift in Sources */,
				281B40872E0A40B500939041 /* MineUserVC.swift in Sources */,
				2840EDA62D82F32A000A7EA5 /* DyNoticeCell.swift in Sources */,
				2828054E2D8995220015FA35 /* UserPageHeaderView.swift in Sources */,
				2825A3F02DF95AD500EE425C /* TuLongAttCarouseView.swift in Sources */,
				28AFCDC12DB73945003CE291 /* RoomTypeModel.swift in Sources */,
				28AFCE092DB9CA64003CE291 /* TickHub.swift in Sources */,
				287E94112DE54DE2004EEDFB /* RoomInviteMsgCell.swift in Sources */,
				28BB749E2DD19D44004B9997 /* BossJpMainSheetView.swift in Sources */,
				288548E62D6F3F200047A69D /* PassWordAccountVC.swift in Sources */,
				28AFCF112DBB7297003CE291 /* RoomUserInfoModel.swift in Sources */,
				288549562D793E0E0047A69D /* EmojiMainView.swift in Sources */,
				283264662D9E2459007465A1 /* GiftItemCell.swift in Sources */,
				95DAA5B92D2D79E100126D4E /* BlockedUserManager.swift in Sources */,
				282806C72D92D4F10015FA35 /* GiftMsgCell.swift in Sources */,
				2825A3C62DF7C8A200EE425C /* SheetWkWebView.swift in Sources */,
				2832600F2D9A79E8007465A1 /* FriendModel.swift in Sources */,
				287860012E0BEBA200367787 /* PurchaseSuccessView.swift in Sources */,
				287860062E0BF64F00367787 /* MyPackListVC.swift in Sources */,
				2866EB842DC1D766005B0D13 /* RankListVC.swift in Sources */,
				281B40632E02ACFB00939041 /* CustomConfigVC.swift in Sources */,
				288549052D753BFB0047A69D /* ExpandPushAnimator.swift in Sources */,
				2828064F2D8C12570015FA35 /* PearlsListVC.swift in Sources */,
				959B2B072CE85D960047E096 /* SyteamListVC.swift in Sources */,
				28AFCDFF2DB79CD4003CE291 /* RoomFloatingWindow.swift in Sources */,
				28B931C82D7AF12800DE4F8E /* VoicePostView.swift in Sources */,
				288548FE2D71A5F30047A69D /* BannerModel.swift in Sources */,
				28AFCDBD2DB73070003CE291 /* CreateRoomVC.swift in Sources */,
				282806AE2D92A1670015FA35 /* LwChatListVC.swift in Sources */,
				28AFCF9F2DBF5A03003CE291 /* TagListView.swift in Sources */,
				28E21AB02D6EED690007ED12 /* PasswordSettingVC.swift in Sources */,
				288548ED2D7002F30047A69D /* LWUserManger.swift in Sources */,
				2849F8B22DD5E20300FA9D53 /* UserLevelMainVC.swift in Sources */,
				288549202D7698BF0047A69D /* DynamicDetailHeaderView.swift in Sources */,
				282806B12D92A3A10015FA35 /* LwChatUserCell.swift in Sources */,
				281C6FE12E000389002F9D99 /* GameUtils.swift in Sources */,
				28E21A732D6C6C340007ED12 /* AppConfigUI_Ext.swift in Sources */,
				282804DD2D8817140015FA35 /* TagBaseModel.swift in Sources */,
				28C1ED182DDAC203006F0442 /* CurrencyBottomView.swift in Sources */,
				287E93542DDEB655004EEDFB /* RoomNoticeEditVC.swift in Sources */,
				283263E22D9BBF37007465A1 /* PopoverMenuViewController.swift in Sources */,
				954188212D0561F90043071D /* BlacklistManager.swift in Sources */,
				288549162D7595150047A69D /* DynamicMainVC.swift in Sources */,
				281B40662E02D39600939041 /* MansionMainVC.swift in Sources */,
				2825A3EE2DF952BC00EE425C /* TuLongTickerManager.swift in Sources */,
				28B931CA2D7AF93900DE4F8E /* AudioRecordSession.swift in Sources */,
				282806572D9136D60015FA35 /* UserRechargeModel.swift in Sources */,
				28F643EB2D953B1B00487E43 /* AudioVisualizerView.swift in Sources */,
				2866EB822DC1D75B005B0D13 /* OnlineListVC.swift in Sources */,
				282806522D90F42F0015FA35 /* PearlsListCell.swift in Sources */,
				288549032D71B9B50047A69D /* TopicModel.swift in Sources */,
				283263DE2D9B85AA007465A1 /* UserGiftListCell.swift in Sources */,
				28E21A9B2D6D98590007ED12 /* PrivacySettingsSheetView.swift in Sources */,
				285DA64A2DFACFF500BDCDAD /* BossRedRobView.swift in Sources */,
				289F9F692DCDA50F00D7522D /* BossValueView.swift in Sources */,
				288549142D7594A40047A69D /* DynamicBaseModel.swift in Sources */,
				285DA6512DFBFF2900BDCDAD /* UserPkSheetView.swift in Sources */,
				2825A3BE2DF6BF1400EE425C /* RoomTaskModel.swift in Sources */,
				28AFCDAA2DB5E41B003CE291 /* RoomSnapshot.swift in Sources */,
				2849F8A12DD43EAE00FA9D53 /* PointsExchangeView.swift in Sources */,
				28785FF52E0AAFA200367787 /* RingListVC.swift in Sources */,
				95DAA59D2D2D6CA500126D4E /* PostModel.swift in Sources */,
				95DAA5AF2D2D731200126D4E /* MusicianCell.swift in Sources */,
				28F6433F2D93F2BE00487E43 /* ChatDetailNavView.swift in Sources */,
				287E940F2DE47358004EEDFB /* RoomSongCell.swift in Sources */,
				95DAA5B32D2D750D00126D4E /* UserProfileViewController.swift in Sources */,
				28AFCF492DBDCD3A003CE291 /* LuckSproutButton.swift in Sources */,
				289F9E942DCC4E7000D7522D /* BoosMainSheetView.swift in Sources */,
				288549502D7938300047A69D /* EmojiModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		959B2AD82CE840AA0047E096 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				959B2AD92CE840AA0047E096 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		959B2ADC2CE840AA0047E096 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		959B2ADD2CE840AA0047E096 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		959B2ADF2CE840AA0047E096 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 80AB014BE5AAB7F6B13EA95F /* Pods-YINDONG.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = YINDONG/YINDONGDebug.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2025062401;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = DSXCN7Z225;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/YINDONG/Main/Manger/QuickLogin",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = YINDONG/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "声光";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.social-networking";
				INFOPLIST_KEY_NSCameraUsageDescription = "App 想要使用摄像头，用于拍照,使用在个人头像与聊天中";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "App 想要获取您的位置信息，用于获取附近歌唱活动推荐给你,帮助您更好地浏览";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "App 想要使用麦克风，用于录制曲谱的音频";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "App 想要访问照片库，用于上传照片，使用在个人头像与聊天中";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "App 访问设备标识符权限仅用于优化浏览体验及优化用户体验,不会获取您的其他隐私信息";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.6;
				OTHER_LDFLAGS = (
					"-ObjC",
					"-framework",
					YTXOperators,
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.yindong.dzcp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = lewanDev;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "YINDONG/Main/Manger/NetTool/YINDONG-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		959B2AE02CE840AA0047E096 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6AFDABB36EC1DFB76A8E2A8B /* Pods-YINDONG.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = YINDONG/YINDONG.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2025062401;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = DSXCN7Z225;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/YINDONG/Main/Manger/QuickLogin",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = YINDONG/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "声光";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.social-networking";
				INFOPLIST_KEY_NSCameraUsageDescription = "App 想要使用摄像头，用于拍照,使用在个人头像与聊天中";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "App 想要获取您的位置信息，用于获取附近歌唱活动推荐给你,帮助您更好地浏览";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "App 想要使用麦克风，用于录制曲谱的音频";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "App 想要访问照片库，用于上传照片，使用在个人头像与聊天中";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "App 访问设备标识符权限仅用于优化浏览体验及优化用户体验,不会获取您的其他隐私信息";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.6;
				OTHER_LDFLAGS = (
					"-ObjC",
					"-framework",
					YTXOperators,
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.yindong.dzcp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = LW_Adhoc;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "YINDONG/Main/Manger/NetTool/YINDONG-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		959B2AC52CE840A90047E096 /* Build configuration list for PBXProject "YINDONG" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				959B2ADC2CE840AA0047E096 /* Debug */,
				959B2ADD2CE840AA0047E096 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		959B2ADE2CE840AA0047E096 /* Build configuration list for PBXNativeTarget "YINDONG" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				959B2ADF2CE840AA0047E096 /* Debug */,
				959B2AE02CE840AA0047E096 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 959B2AC22CE840A90047E096 /* Project object */;
}
