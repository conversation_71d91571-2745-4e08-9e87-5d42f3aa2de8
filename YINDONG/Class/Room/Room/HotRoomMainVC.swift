//
//  HotRoomMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/4/17.
//

import UIKit
import IQKeyboardManagerSwift
import IQKeyboardToolbarManager

class HotRoomMainVC: YinDBaseVC {
    
    private lazy var vm = RoomViewModel(roomId: roomNum ?? "")
    
    // UI 组件
    private var roomContainerView: RoomContainerView!
    // 滚动页面相关
    private var pageScrollView: UIScrollView!
    private var clearScreenView: UIView!          // 清屏页面
    private var mainContentView: UIView!          // 主内容页面
    private var exitClearScreenButton: UIButton!  // 退出清屏按钮
    private var isInClearScreenMode: Bool = false // 是否处于清屏模式
    // 侧边栏相关
    private var sideMenuView: SideMenuView?
    // 移除边缘手势相关变量，避免交互冲突
    // private var edgeScreenRecognizer: UIScreenEdgePanGestureRecognizer!
    // private var isDraggingSideMenu = false
    
    lazy var topInfoView: RoomInfoTopView = {
        let infoV = RoomInfoTopView()
        return infoV
    }()
    
    // 数据
    private var currentLayout: LayoutStrategy = .standardHostTop
    
    var roomNum: String?
    
    lazy var roomBackgroundView: RoomBgView = {
        let bgView = RoomBgView()
        return bgView
    }()
    
    var roomInfoModel: RoomDetailModel? {
        return vm.infoModel
    }
    
    lazy var backRandImgV: UIImageView = {
        let imgV = UIImageView()
        imgV.backgroundColor = .init(hex: 0x1e1e1e)
        imgV.layerCornerRadius = 1
        imgV.contentMode = .scaleAspectFill
        return imgV
    }()
    
    lazy var msgVC: RoomPublicMessageVC = {
        let msgV = RoomPublicMessageVC()
        return msgV
    }()
    
    lazy var chatBarConfigView: RoomChatBarView = {
        let vv = RoomChatBarView(viewModel: vm)
        return vv
    }()
    
    ///boss
    lazy var bossAction: BossAttackFloatingView = {
        let vv = BossAttackFloatingView(frame: CGRect(x: view.width - 89, y: AppTool.safeAreaTopHeight + 66, width: 79, height: 79))
        return vv
    }()
    
    lazy var songItem: InfoItemBaseView = {
        let vv = InfoItemBaseView(rightImg: "music_icon_room", leftTitle: "", isLeft: true, space: 5, imgWH: CGSizeMake(16, 16), margin: 12)
        vv.titleLab.font = .regular(11)
        vv.titleLab.textColor = .white
        vv.isHidden = true
        vv.backgroundColor = .init(hex: 0xFFFFFF, transparency: 0.1)
        vv.layerCornerRadius = 12
        vv.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(showVoice)))
        return vv
    }()
    
    //屠龙 和 任务
    lazy var taskView: TaskCarousel = {
        let vv = TaskCarousel()
        return vv
    }()
    
    lazy var adView: AdvertView = {
        let vv = AdvertView()
        return vv
    }()
    
    //红包 展示View
    lazy var redBagView: RedActView = {
        let vv = RedActView()
        vv.isHidden = true
        vv.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(openRedBag)))
        return vv
    }()
    
    //展示音乐的 View
    @objc
    func showVoice() {
        let music = RoomSongMainView()
        music.present(in: nil)
    }
    
    @objc
    func openRedBag() {
        let vc = BossRedRedBegainV()
        vc.configure(with: redBagView.redBagModel!)
        vc.show()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Do any additional setup after loading the view.
        
        isNavigationBarVisible = false
        
        // 绑定输出
        vm.snapshotRelay
            .compactMap { $0 }
            .observe(on: MainScheduler.instance)
            .bind(onNext: { [weak self] snap in
                self?.reloadInfoData(snap: snap)
            })
            .disposed(by: baseDisposeBag)
        
        vm.onToast = {
            ProgressHUDManager.showTextMessage($0)
        }
        
        vm.onLayoutChange = { [weak self] layout in
            self?.changeToLayout(at: layout)
        }
        
        vm.msgAddSub.subscribe(onNext: { [weak self] msg in
            guard let self = self else { return }
            self.msgVC.insertMsg(msg)
        }).disposed(by: baseDisposeBag)
        
        vm.liveInfoUser.subscribe(onNext: { [weak self] msg in
            guard let self = self else { return }
            self.topInfoView.userBgView.vModel = self.vm
            
        }).disposed(by: baseDisposeBag)
        
        vm.exitRoomBehavir.subscribe(onNext: { [weak self] code in
            guard let self = self, code == true else { return }
            self.outAction()
        }).disposed(by: baseDisposeBag)
        
        
        vm.hotRedBag.subscribe(onNext: { [weak self] datas in
            guard let self = self, var mm = datas?.first else { return }
            if mm.redEnvelopesId <= 0 { return }
            self.redBagView.isHidden = false
            self.redBagView.configure(with: mm)
        }).disposed(by: baseDisposeBag)
        
        // 输入
        vm.enterRoom()
        
        
        scrollToMainPage(animated: false)
        
        
        roomContainerView.onSeatTap = { [weak self] seat, index in
            self?.vm.tapSeat(index: index)
        }
        
        roomContainerView.centerView.vm = vm
        
        loadData()
        
        ///退出房间
        RoomManger.shared.exitRoomRelay.subscribe(onNext: { [weak self] isOk in
            guard let isOk = isOk, let self = self, isOk else { return }
            
            if RoomManger.shared.curretRole == .owner {
                self.roomBackgroundView.stopVideo()
                RoomManger.shared.exitRoom { model in
                    self.dismiss(animated: false) {
                        let vc = RoomSummaryVC()
                        vc.exitInfo = model
                        AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
                    }
                }
                return
            }
            
            
            if RoomFloatingWindowManager.shared.isFloatingWindowVisible  {
                RoomFloatingWindowManager.shared.cleanup()
                ProgressHUDManager.showTextMessage("房间已关闭")
                return
            }
            //关闭前先保留一份值
            let detailInfo = vm.liveInfoUser.value
            let seatInfo = RoomManger.shared.snapModel?.seats.first
            self.outAction()
            Async.main(after: 0.2) {
                let vc = AudienceRoomClosedVC()
                vc.info = detailInfo
                vc.seatInfo = seatInfo
                AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
            }
            
            
        }).disposed(by: baseDisposeBag)
        
        
        //屠龙红包雨 触发
        vm.tlRedyAddSub.subscribe(onNext: { [weak self] id in
            guard let self = self else { return }
            Async.main(after: 0.01) {
                let rain = RedPacketRainView()
                // 设置红包点击回调
                rain.onPacketTapped = { [weak self] index, resultCallback in
                    TLog("红包雨 - 点击红包 \(index)，发送接口请求...")
                    // 调用抢红包接口
                    self?.requestGrabRedPacket(isHot: "2", index: index, redId: id) { isSuccess, text in
                        // 将服务器结果回调给红包雨
                        resultCallback(isSuccess, text)
                    }
                }
                
                //结束弹窗
                rain.endAction = { [weak self] in
                    guard let self = self else { return }
                    // 请求红包结果
                    NetworkUtility.request(target: .tuLongEnvelopes(["redEnvelopesId": id]), model: LWUserModel.self) { result in
                        DispatchQueue.main.async {
                            if result.isError {
                                return
                            }
                            ///有数据的情况 弹出结算榜单
                            if let user = result.model, isValid(user.userNumber) {
                                let popView = TuLongRedLuckPopView()
                                popView.lunckyUser = user
                                
                                var i = 0
                                while i < self.vm.redGift.count {
                                    let current = self.vm.redGift[i]
                                    var j = i + 1
                                    while j < self.vm.redGift.count {
                                        if self.vm.redGift[j].id == current.id {
                                            current.count += self.vm.redGift[j].count
                                            self.vm.redGift.remove(at: j)  // 删除重复项
                                        } else {
                                            j += 1
                                        }
                                    }
                                    i += 1
                                }
                                popView.prizes = self.vm.redGift
                                popView.show()
                            }
                        }
                    }
                }
                rain.startWithCountdown(on: self.view, count: 3)
            }
        }).disposed(by: baseDisposeBag)
        
        //调试代码
//        Async.main(after: 1) {

//        }
        ///加入系统公告
        Async.main(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.vm.creatSytemNotice()
        }
        
//        ScreenManagement.shared.setContainer(self.view)
//        Async.main(after: 1) {
//            ScreenManagement.shared.addTestColorBlocks()
//        }
//        
//        Async.main(after: 2) {
//            ScreenManagement.shared.addTestColorBlocks()
//        }

    }
    
    func reloadInfoData(snap: RoomSnapshot) {
        // 设置背景和其他不会引起布局变化的UI元素
        roomBackgroundView.setBackground(urlString: snap.config.background)
        self.topInfoView.updateCount(count: snap.detail.roomPeopleCout)
        
        // 检查房间类型，看是否需要更新布局
        self.checkAndUpdateLayoutIfNeeded()
        
        // 更新容器高度 - 等待布局完成后再更新高度
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.updateRoomContainerHeight()
        }
        
        if snap.detail.musicName.count > 0 {
            songItem.titleLab.text = snap.detail.musicName
            songItem.isHidden = false
        } else {
            songItem.isHidden = true
        }
        
    }
    
    // 检查并在需要时更新布局
    private func checkAndUpdateLayoutIfNeeded() {
        // 根据房间配置和类型选择合适的布局策略
        let newLayout = vm.determineLayout()
        
        // 只有在布局类型实际变化时才应用变更
        if currentLayout != newLayout {
            self.changeToLayout(at: newLayout)
        }
    }
    
    func changeToLayout(at index: LayoutStrategy) {
        // 如果布局没变，不做任何事情
        if currentLayout == index { return }
        
        let oldLayout = currentLayout
        currentLayout = index
        
        // 应用新布局到容器
        roomContainerView.updateLayout(strategy: currentLayout)
        
        // 等布局动画完成后，确保更新容器高度
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            self.updateRoomContainerHeight()
        }
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        view.addSubview(roomBackgroundView)
        roomBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        pageScrollView = UIScrollView()
        pageScrollView.isPagingEnabled = true
        pageScrollView.showsHorizontalScrollIndicator = false
        pageScrollView.bounces = false
        pageScrollView.delegate = self
        pageScrollView.isScrollEnabled = false
        view.addSubview(pageScrollView)
        
        // 重要：滚动视图不占据整个屏幕，只占据下半部分
        pageScrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(screenWidth)
            make.height.equalTo(screenHeight)
        }
        
        // 设置滚动视图内容尺寸
        let contentWidth = screenWidth * 2
        let contentHeight = screenHeight
        pageScrollView.contentSize = CGSize(width: contentWidth, height: contentHeight)
        
        
        clearScreenView = UIView()
        clearScreenView.backgroundColor = .clear
        pageScrollView.addSubview(clearScreenView)
        
        clearScreenView.snp.makeConstraints { make in
            make.left.top.equalToSuperview()
            make.width.equalTo(screenWidth)
            make.height.equalTo(screenHeight)
        }
        
        // 添加退出清屏按钮
        exitClearScreenButton = UIButton(type: .system)
        exitClearScreenButton.setTitle("退出清屏", for: .normal)
        exitClearScreenButton.setTitleColor(.white, for: .normal)
        exitClearScreenButton.backgroundColor = UIColor(white: 0.3, alpha: 0.7)
        exitClearScreenButton.layer.cornerRadius = 20
        exitClearScreenButton.addTarget(self, action: #selector(exitClearScreen), for: .touchUpInside)
        clearScreenView.addSubview(exitClearScreenButton)
        
        exitClearScreenButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-20)
            make.width.equalTo(100)
            make.height.equalTo(40)
        }
        
        mainContentView = UIView()
        mainContentView.backgroundColor = .clear
        pageScrollView.addSubview(mainContentView)
        
        mainContentView.snp.makeConstraints { make in
            make.left.equalTo(clearScreenView.snp.right)
            make.top.equalToSuperview()
            make.width.equalTo(screenWidth)
            make.height.equalTo(screenHeight)
        }
        
        self.addChild(msgVC)
        mainContentView.addSubview(msgVC.view)
        // 重要：创建RoomContainerView之前先确定初始布局
        setupRoomContainerWithLayout()
        
        mainContentView.addSubview(topInfoView)
        topInfoView.showMenuCallBack = { [weak self] in
            self?.exitButtonTapped()
        }
        topInfoView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.safeAreaTopHeight + 5)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        mainContentView.addSubview(chatBarConfigView)
        chatBarConfigView.snp.makeConstraints { make in
            make.bottom.equalTo(-(AppTool.safeAreaBottomHeight + 5))
            make.left.right.equalToSuperview()
            make.height.equalTo(35)
        }
        
        mainContentView.addSubview(taskView)
        taskView.vm = self.vm
        taskView.snp.makeConstraints { make in
            make.centerY.equalToSuperview().multipliedBy(1.26)
            make.right.equalTo(-17)
            make.width.equalTo(64)
            make.height.equalTo(78)
        }
        
        mainContentView.addSubview(adView)
        adView.snp.makeConstraints { make in
            make.centerX.equalTo(taskView)
            make.top.equalTo(taskView.snp.bottom).offset(70)
            make.width.height.equalTo(60)
        }
        
        mainContentView.addSubview(redBagView)
        redBagView.snp.makeConstraints { make in
            make.centerX.equalTo(taskView)
            make.top.equalTo(taskView.snp.bottom).offset(15)
            make.width.equalTo(44)
            make.height.equalTo(49)
        }
        
        //房间红包回调
        redBagView.onStateChanged = { [weak self] isOpen in
            guard let self = self else { return }
            if !isOpen { return }
            self.vm.hotRedGift.removeAll()
            let rain = BossRedRobView()
            rain.onPacketTapped = { [weak self] index, completion in
                guard let self = self else {
                    completion(false)
                    return
                }
                self.requestGrabRedPacket(isHot: "1", index: index, redId: self.redBagView.redBagModel?.redEnvelopesId.string) { isSuccess, text in
                    // 将服务器结果回调给红包雨
                    completion(isSuccess)
                }
            }
            // 设置结束回调
            rain.endAction = { [weak self] in
                guard let self = self else { return }
                self.redBagView.isHidden = true
                NetworkUtility.request(target: .roomRedEnvelopesUser(["redEnvelopesId": redBagView.redBagModel?.redEnvelopesId ?? 0]), model: LWUserModel.self) { result in
                    if result.isError { return }
                    ///有数据的情况 弹出结算榜单
                    if let user = result.model, isValid(user.userNumber) {
                        let popView = RedLuckUserPopV()
                        popView.lunckyUser = user
                        popView.datas = self.vm.hotRedGift
                        popView.show()
                    }
                }
            }
            rain.startRain(on: self.view)
        }
        
        // 创建侧边菜单（确保在最顶层）
        setupSideMenu()
        
        vm.giftManger()
        vm.peiBanInfoGet()
        
    }
    
    // 预先确定布局类型并创建RoomContainerView
    private func setupRoomContainerWithLayout() {
        // 根据房间类型预先确定布局
        let initialLayout = vm.determineLayout()
        currentLayout = initialLayout
        
        // 使用预先确定的布局创建RoomContainerView
        roomContainerView = RoomContainerView(frame: .zero, layoutType: initialLayout)
        view.addSubview(roomContainerView)
        
        // 设置布局约束
        roomContainerView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.safeAreaTopHeight + 50)
            make.left.right.equalToSuperview()
            // 使用计算出的最优高度
            make.height.equalTo(roomContainerView.getOptimalHeight())
        }
        
        
        msgVC.view.snp.makeConstraints { make in
            make.top.equalTo(roomContainerView.snp.bottom)
            make.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.65)
            make.bottom.equalTo(-(AppTool.safeAreaBottomHeight + 44))
        }
        
        view.addSubview(bossAction)
        bossAction.viewModel = vm
        
        view.addSubview(songItem)
        songItem.snp.makeConstraints { make in
            make.top.equalTo(AppTool.safeAreaTopHeight + 60)
            make.left.equalTo(15)
            make.height.equalTo(24)
            make.width.lessThanOrEqualTo(95)
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 恢复视频背景播放
        roomBackgroundView.resumeVideo()
        IQKeyboardManager.shared.isEnabled = false
        IQKeyboardToolbarManager.shared.isEnabled = false
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        // 暂停视频背景播放
        roomBackgroundView.pauseVideo()
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardToolbarManager.shared.isEnabled = true
    }
    
    @objc
    func outAction() {
        
        taskView.clearTimer()
        
        ///房主退房 单独处理
        if RoomManger.shared.curretRole == .owner {
            self.roomBackgroundView.stopVideo()
            RoomManger.shared.exitRoom { model in
                self.dismiss(animated: false) { [weak self] in
                    self?.vm.exitRoom()
                    let vc = RoomSummaryVC()
                    vc.exitInfo = model
                    AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
                    
                }
            }
            return
        }
        
        
        let taskV = ExitRoomTaskPopView()
        if vm.showAudienceExitRoomDialog(topTaskData: taskView.allTask, onTick: { content, reward in
            taskV.messageLabel.attributed.text = "再玩派对\(content, .font(.bold(13)), .foreground(.white))，就可以获得\(reward, .font(.bold(13)), .foreground(.white))，确认退出吗?"
            
        }, onFinish: {
        }) {
            taskV.exitAction = { [weak self] in
                guard let self = self else { return }
                
                // 退出房间时停止视频
                roomBackgroundView.stopVideo()
                RoomManger.shared.exitRoom()
                self.dismiss(animated: false) { [weak self] in
                    self?.vm.exitRoom()
                }
            }
            taskV.show()
            return
        }
        taskV.removeSubviews()
        taskV.removeFromSuperview()
        
        // 退出房间时停止视频
        roomBackgroundView.stopVideo()
        RoomManger.shared.exitRoom()
        self.dismiss(animated: false) { [weak self] in
            self?.vm.exitRoom()
        }
    }
    
    override func loadData() {
        
        self.vm.reloadRoomDetailInfo { [weak self] in
            guard let self = self else { return }
            self.topInfoView.userBgView.vModel = self.vm
            self.rankInfoData()
            TickHub.shared.infoHeabet()
            RoomManger.shared.loadAllCount()
            RoomManger.shared.updateHotInfo(time: self.vm.infoModel?.voiceLiveVo?.labelStartDate?.int ?? 0, isHot: self.vm.infoModel?.voiceLiveVo?.isHot == 1)
            
            ///如果我是房主，同步管理员 id
            if RoomManger.shared.curretRole == .owner, let ids = self.vm.infoModel?.voiceLiveAdmin.compactMap({ $0.imNumber.string }), ids.count > 0 {
                RoomManger.shared.addOrDelManger(imNums: ids)
            }
            
            ///塞入房间公告
            Async.main(after: 0.1) { [weak self] in
                guard let self = self else { return }
                if let notice = self.vm.infoModel?.voiceLiveVo?.roomNotice, isValid(notice) {
                    vm.creatRoomNotice(tt: notice)
                }
                vm.getRedBag()
            }
            
        }
        
    }
    
    ///获取榜单信息
    func rankInfoData() {
        var dict = self.creatDict()
        dict["liveRoomId"] = self.roomInfoModel?.roomid ?? 0
        dict["type"] = 0
        NetworkUtility.request(target: .roomRank(dict), model: LWUserModel.self, isList: true) { result in
            if result.isError { return }
            self.topInfoView.ranks = result.modelArr
        }
    }
    
    
    func updateRoomContainerHeight() {
        // 获取根据布局和麦位计算出的最优高度
        let optimalHeight = self.roomContainerView.getOptimalHeight()
        
        // 使用动画更新高度约束
        UIView.animate(withDuration: 0.2) {
            self.roomContainerView.snp.updateConstraints { make in
                make.height.equalTo(optimalHeight)
            }
            self.view.layoutIfNeeded()
        }
    }
    
    // 退出清屏模式
    @objc private func exitClearScreen() {
        scrollToMainPage(animated: true)
    }
    
    // 滚动到主内容页
    private func scrollToMainPage(animated: Bool) {
        pageScrollView.setContentOffset(CGPoint(x: screenWidth, y: 0), animated: animated)
        
        // 即使动画未完成也立即更新状态
        isInClearScreenMode = false
        updateUIForCurrentPage()
    }
    
    // 根据当前页面更新UI
    private func updateUIForCurrentPage() {
        
        
        
    }
    
    // 获取菜单实际宽度
    private func menuWidth() -> CGFloat {
        return UIScreen.main.bounds.width * 0.7
    }
    
    // 显示侧边栏
    private func showSideMenu() {
        // 每次显示前确保菜单在视图层级的最上层
        if let menuView = sideMenuView {
            view.bringSubviewToFront(menuView)
            menuView.show()
        }
    }
    
    // 设置侧边菜单
    private func setupSideMenu() {
        // 创建侧边菜单实例
        if sideMenuView == nil {
            let sideMenu = SideMenuView(frame: view.bounds)
            sideMenu.delegate = self
            sideMenuView = sideMenu
        }
        guard let sideMenuView = sideMenuView else { return }
        // 关键修改：确保添加在最顶层 - 在所有视图之上
        view.addSubview(sideMenuView)
    }
    
    // 处理退出按钮点击
    @objc private func exitButtonTapped() {
        showSideMenu()
    }
    
    // 移除边缘手势处理方法
    
    
    // MARK: - 抢红包接口
    private func requestGrabRedPacket(isHot: String, index: Int, redId: String?, completion: @escaping (Bool, String) -> Void) {
        // 构建请求参数
        var dict = self.creatDict()
        dict["batchNo"] = index
        dict["redEnvelopesId"] = redId ?? ""

        TLog("发送抢红包请求 - 下标: \(index)")
        // 发送网络请求
        NetworkUtility.request(target: isHot == "1" ? .roomLiveRed(dict) : .tLongGrabRedPacket(dict), model: GiftBaseModel.self) { result in
            DispatchQueue.main.async {
                if result.isError {
                    TLog("抢红包接口失败 - 下标: \(index), 错误:")
                    completion(false, "") // 接口失败，返回失败
                } else {

                    if let model = result.model, isValid(model.name) {
                        if isHot == "1" {
                            self.vm.hotRedGift.append(model)
                        } else {
                            self.vm.redGift.append(model)
                        }
                        completion(true, "\(model.name ?? "")")
                        return
                    }
                    completion(false, "")
                }
            }
        }
    }
    
    deinit {
        TLog("🔄 HotRoomMainVC.deinit - 开始")
        // 再次确保清理 vm
        if vm != nil {
            TLog("🔄 HotRoomMainVC.deinit - vm 仍然存在，进行最后清理")
            vm.exitRoom()
        }
        TLog("🔄 HotRoomMainVC.deinit - 结束")
    }
    
}

// MARK: - UIScrollViewDelegate
extension HotRoomMainVC: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 更新清屏状态
        let currentPage = round(scrollView.contentOffset.x / screenWidth)
        isInClearScreenMode = (currentPage == 0)
        // 调试信息，检查滚动是否正常工作
        print("滚动中: x=\(scrollView.contentOffset.x), page=\(currentPage)")
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateUIForCurrentPage()
        print("滚动结束(减速): x=\(scrollView.contentOffset.x)")
    }
    
    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        updateUIForCurrentPage()
        print("滚动结束(动画): x=\(scrollView.contentOffset.x)")
    }
}



// MARK: - SideMenuViewDelegate
extension HotRoomMainVC: SideMenuViewDelegate {
    
    func sideMenuDidSelectScale() {
        RoomFloatingWindowManager.shared.minimizeRoom(self, fromView: view)
    }
    
    ///房间退出
    func sideMenuDidSelectExit() {
        
        if RoomManger.shared.curretRole == .owner {
            let msgV = MessagePopupView(title: "", content: "确定关闭房间？")
            msgV.onAction = { [weak self] isc, v in
                if isc { return }
                self?.outAction()
            }
            msgV.show()
            return
        }
        
        outAction()
    }
}
