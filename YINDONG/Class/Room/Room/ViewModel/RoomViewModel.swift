//
//  RoomViewModel.swift
//  YINDONG
//
//  Created by jj on 2025/4/24.
//

import UIKit


/// 1秒的毫秒数
private let SECOND: TimeInterval = 1
/// 1分钟的毫秒数
private let MINUTE: TimeInterval = 60 * SECOND

class RoomViewModel: NSObject {
    
    // MARK: - Public output
    let snapshotRelay = BehaviorRelay<RoomSnapshot?>(value: nil) // UI 订阅
    var onToast: ((String) -> Void)?                             // 轻量回调
    var onLayoutChange: ((LayoutStrategy) -> Void)?
    
    ///用户进房时间（毫秒）
    var userGoRoomTime: Int64 = 0
    
    // MARK: - Private
    private var disposeBag = DisposeBag()
    private var roomId: String
    
    var msgAddSub = PublishSubject<RoomBaseMsgModel>()

    var tlMsgAddSub = PublishSubject<RoomBaseMsgModel>()
    var tlRedyAddSub = PublishSubject<String>()
    
    var infoModel: RoomDetailModel?
    ///房主信息
    var liveInfoUser = BehaviorRelay<RoomUserInfoModel?>(value: nil)
    
    var exitRoomBehavir = BehaviorRelay<Bool?>(value: nil)
    
    // 礼物管理器
    var giftManager: GiftQueManager<QueGift>?

    //任务资料
    var taskRelay = BehaviorRelay<RoomCompanyTaskModel?>(value: nil)
    
    //屠龙红包雨获得奖品
    var redGift: [GiftBaseModel] = []
    var hotRedGift: [GiftBaseModel] = []
    
    //hot 红包同步
    var hotRedBag = BehaviorRelay<[RedBagBaseModel]?>(value: nil)

    // MARK: - Init
    init(roomId: String) {
        self.roomId = roomId
        ///记录用户进房的时间
        userGoRoomTime = Int64(Date().timeIntervalSince1970) * 1000
        super.init()
        // 监听 RoomManager 快照
        RoomManger.shared.snapshotRelay
            .bind(to: snapshotRelay)
            .disposed(by: disposeBag)
        
        // 如果需要：监听房间类型变化决定 layout
        snapshotRelay
            .compactMap { $0?.config }
            .map { [weak self] _ in
                guard let self = self else { return .standardHostTop }
                return self.determineLayout()
            }
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] layout in
                self?.onLayoutChange?(layout)
            })
            .disposed(by: disposeBag)
        
        
        ///监听 detail改变
        snapshotRelay.compactMap({ $0?.detail }).distinctUntilChanged().subscribe(onNext: { [weak self] detail in
            guard let self = self else { return }
            
            self.infoModel?.voiceLiveVo?.password = detail.roomPassword
            
            Async.main(after: 0.2) {
                if detail.isAllUserMute == true {
                    let msg = RoomBaseMsgModel()
                    msg.msgType = .roomPubChatLocal
                    msg.txtContent = "公屏已关闭"
                    self.msgAddSub.onNext(msg)
                }
            }
            
        }).disposed(by: disposeBag)
        
        RoomManger.shared.pubCloseSubject.subscribe(onNext: { [weak self] isClose in
            guard let self = self else { return }
            Async.main(after: 0.1) {
                let mm = isClose ? "已关闭" : "已开启"
                let msg = RoomBaseMsgModel()
                msg.msgType = .roomPubChatLocal
                msg.txtContent = "公屏\(mm)"
                self.msgAddSub.onNext(msg)
            }
        }).disposed(by: disposeBag)
        
        
        // 房间跳转个人主页的通知
        RoomManger.shared.seatActionSubject.subscribe(onNext: { [weak self] type, info in
            guard let self = self, let infoConfig = info else { return }
            ///获取当前麦位下标
            let index = RoomManger.shared.cheakUserSeatIndex(id: infoConfig.imNumber.string)
            
            if type == .ban || type == .unban {
                self.banSeatUser(info: infoConfig, type: type)
                return
            }
            
            if type == .kickFromRoom {
                RoomManger.shared.kickOutRoom(infoConfig)
                return
            }
            
            SeatActionManager.seatAction(type: type, seatIndex: index, uid: infoConfig.imNumber.string)
            
        }).disposed(by: disposeBag)
        
        
        RoomManger.shared.seatSheetCardSubject.subscribe(onNext: { [weak self] id in
            guard let _ = self else { return }
            guard let id = id else { return }
            let sheetV = RoomInfoCardSheetView(userID: id)
            sheetV.present(in: nil)
            
        }).disposed(by: disposeBag)
        
        RoomManger.shared.userCardSubject.subscribe(onNext: { [weak self] title, uid in
            guard let self = self else { return }
            switch title {
            case "送礼物":
                let pp = GiftPanelView(index: 0, type: .live)
                pp.viewModel = self
                pp.seletedUid = uid
                pp.present(in: nil)
            case "盘Ta":
                let vc = UserPkSheetView(uid: uid, name: RoomManger.shared.tempUser?.nickName, onlyOne: true)
                vc.present(in: AppTool.getCurrentViewController().view)
            default:
                break
            }
        }).disposed(by: disposeBag)

        T_IMHelper.shared.addDelagete(self)
        
        getUserInfo()
        getUserSkillInfo()
    }
    
    // MARK: - Public actions (由 VC 调用)
    func enterRoom() {
        joinRoomMsg()
        if RoomManger.shared.curretRole == .owner {
            TickHub.shared.hotHeabet()
            TickHub.shared.register(
                name: TickJobName.hostHeartbeat,
                interval: 10) {
                    TickHub.shared.hotHeabet()
                }
        }
        
        TickHub.shared.register(name: TickJobName.roomInfoHeartbeat,
                                interval: 60) {
            TickHub.shared.infoHeabet()
        }
    }
    
    //退出房间 销毁
    func exitRoom() {
        // 重置所有 RxSwift 订阅
        disposeBag = DisposeBag()
        
        if RoomManger.shared.curretIndex >= 0 {
            RoomManger.shared.takeSeat(isUp: false, index: RoomManger.shared.curretIndex) { code, msg in
                
            }
        }
        
        onToast = nil
        onLayoutChange = nil
        
        giftManager?.allGiftsDisappearedCallback = nil
        giftManager?.giftFirstReceivedCallback   = nil
        giftManager?.giftDisappearedCallback     = nil
        
        giftManager = nil
        
        RoomManger.shared.seatSheetCardSubject.accept(nil)
        
        T_IMHelper.shared.removeDelegate(self)
        
   
        TickHub.shared.unregisterAll()
  
        RoomManger.shared.exitRoom()
        
        liveInfoUser.accept(nil)
        exitRoomBehavir.accept(nil)
        snapshotRelay.accept(nil)
        
    }
    
    func tapSeat(index: Int) {
        guard let snap = snapshotRelay.value else { return }
        let seat = snap.seats[index]
        
        switch seat.status {
        case .closed:
            if RoomManger.shared.curretRole == .audience {
                onToast?("该麦位已关闭")
                return
            }
            SeatActionManager.showAction(seat: nil, seatIndex: index)
            
        case .used:
            RoomManger.shared.seatSheetCardSubject.accept(seat.userId)
            
        case .unused: // 空麦
            if RoomManger.shared.curretRole == .audience {
                
                if RoomManger.shared.isOnSeat {
                    RoomManger.shared.changeSeat(index: index) { code, msg in
                        
                    }
                    return
                }
                
                RoomManger.shared.takeSeat(isUp: true, index: index) { [weak self] code, msg in
                    guard let self = self else { return }
                    if code == 0 { self.onToast?(msg) }
                }
                return
            }
            
            SeatActionManager.showAction(seat: nil, seatIndex: index)
        }
    }
    
    // MARK: - Helpers
    func determineLayout() -> LayoutStrategy {
        switch RoomManger.shared.roomType {
        case .gameNine:      return .gameLinear
        case .accompany:     return .companionSplit
        case .familyFire:    return .ringWithHostCenter
        default:             return .standardHostTop
        }
    }
    
    func joinRoomMsg() {
        
        let msg = RoomBaseMsgModel.empty()
        msg.msgType = RoomMessageType.joinRoomPopup
        if let car = UserDisguiseModel.load()?.getRoomCar() {
            msg.carUrl = car.goodsUrlPreview
            msg.carName = car.goodsName
        }
        let dict = msg.toDictionary()
        let data = dict?.jsonData()
        
        T_IMHelper.shared.sendRoomCustomMsg(roomId, message: data) { code, msg in
            
        }
        
    }
    
    func getUserInfo() {
        
        NetworkUtility.request(target: .roomUserInfo(["imId": kuser.imNumber]), model: RoomUserInfoModel.self) { result in
            if result.isError { return }
            self.liveInfoUser.accept(result.model)
        }
        
    }
    
    /// 获取当前麦位信息  默认获取自己的 传入获取他人的
    func getSeatUserInfo(id: String? = nil) -> SeatInfo? {
        guard let snap = snapshotRelay.value else { return nil }
        var arr = snap.seats.filter({ $0.userId == kuser.imNumber.string })
        if let id = id {
            arr = snap.seats.filter({ $0.userId == id })
        }
        return arr.first
    }
    
    ///是否禁言中 默认查询自己,指定id就查询他人
    func isMute(id: String? = nil) -> Bool {
        guard let snap = snapshotRelay.value else { return false }
        ///如果禁言数组有我
        var arr = snap.mute_user.filter({ $0.userId == kuser.imNumber.string })
        if let id = id {
            arr = snap.mute_user.filter({ $0.userId == id })
        }
        if arr.count == 0 { return false }
        
        let seat = arr[0]
        let muteEndTime = seat.muteTime + 5 * 60 * 1000 // 加5分钟，单位是毫秒
        let currentTime = Date().timeIntervalSince1970 * 1000 // 当前时间毫秒
        return currentTime < Double(muteEndTime)
    }
    
    ///自己操作麦克风
    func openCloseSelfMic(index:Int, isOpen: Bool, callback: @escaping RoomActionCallback) {
        guard let snap = snapshotRelay.value else { return }
        var seatInfo = snap.seats[index]
        seatInfo.isSelfMute = isOpen
        let json = seatInfo.toIMJSONString() ?? ""
        var dict: [String: String] = [:]
        dict["seat\(index)"] = json
        RoomManger.shared.updateGroup(info: dict) { code in
            if !code {
                callback(-1000, "不行了")
                return
            }
            callback(code.int, "")
        }
    }
    
    ///设置禁言 解除禁言
    func banSeatUser(info: RoomUserInfoModel, type: SeatActionType) {
        ///禁言涉及到下麦
        if type == .ban {
            
            let messageVV = MessagePopupView(title: "提示", content: "禁言后5分钟内TA将不能在该语音房发言或者连麦，确定禁言？")
            messageVV.onAction = { [weak self] isc, vv in
                guard let self = self, !isc else { return }
                
                ///检查对方是否在麦上
                let index = RoomManger.shared.cheakUserSeatIndex(id: info.imNumber.string)
                if index > 0 {
                    RoomManger.shared.takeSeat(isUp: false, index: index) { code, msg in
                        
                        if code != 1 { return }
                        
                        let snap = RoomManger.shared.snapModel
                        var mute = snap?.mute_user
                        let user = RoomMuteUser(muteTime: Int(Date().timeIntervalSince1970 * 1000), userId: info.imNumber.string)
                        mute?.append(user)
                        let json = mute?.toJSONString()
                        var dict: [String: String] = [:]
                        dict["mute_user"] = json
                        
                        RoomManger.shared.updateGroup(info: dict) { code in
                            if !code { return }
                            ProgressHUDManager.showTextMessage("已禁言")
                            self.sendMsg(info: info, type: type)
                        }
                    }
                    return
                }
                
                let snap = RoomManger.shared.snapModel
                var mute = snap?.mute_user
                let user = RoomMuteUser(muteTime: Int(Date().timeIntervalSince1970 * 1000), userId: info.imNumber.string)
                mute?.append(user)
                let json = mute?.toJSONString()
                var dict: [String: String] = [:]
                dict["mute_user"] = json
                
                RoomManger.shared.updateGroup(info: dict) { code in
                    if !code { return }
                    ProgressHUDManager.showTextMessage("已禁言")
                    self.sendMsg(info: info, type: type)
                }

            }
            messageVV.show()
            
           
            return
        }
        
        let snap = RoomManger.shared.snapModel
        var mute = snap?.mute_user
        mute?.removeAll(where: { $0.userId == info.imNumber.string })
        let json = mute?.toJSONString()
        var dict: [String: String] = [:]
        dict["mute_user"] = json
        
        RoomManger.shared.updateGroup(info: dict) { [weak self] code in
            guard let self = self else { return }
            if !code { return }
            ProgressHUDManager.showTextMessage("已解除禁言")
            self.sendMsg(info: info, type: type)
        }
    }
    
    func sendMsg(info: RoomUserInfoModel, type: SeatActionType) {
        
        let vv2 = RoomManger.shared.curretRole == .owner ? "房主" : "管理员"

        var uInfo = SeatInfo()
        uInfo.userName = info.nickName
        uInfo.userId = info.imNumber.string
        
        var typeTitle = type.title
        let msg = MsgRoomTextMsg(userId: kuser.imNumber.string, atUsers: [uInfo], msgType: .muteToggle, txtContent: "\(uInfo.userName ?? "")被\(vv2)\(typeTitle)")
        RoomManger.shared.sendRoomMsg(msg: msg)
    }
    
    ///发送关注消息
    func sendFollow() {
        guard var fzSeat = RoomManger.shared.snapModel?.seats.first else { return }
        
        var meInfo = SeatInfo()
        meInfo.userName = kuser.nickName
        meInfo.userId = kuser.imNumber.string
        
        fzSeat.userName = fzSeat.profile?.nickName
        
        let msg = MsgRoomTextMsg(userId: meInfo.userId, atUsers: [fzSeat, meInfo], msgType: .followHost, txtContent: "\(meInfo.userName ?? "")关注了\(fzSeat.userName ?? "")")
        RoomManger.shared.sendRoomMsg(msg: msg)
    }
    
    
    ///配置连送的 View
    func giftManger() {
        // 自定义配置样式（在GiftShowView初始化之前配置）
        guard let tempV = AppTool.getCurrentViewController().view else { return }
        let configuration = Configuration.default
        configuration.showDuration = 0.2
        configuration.senderTextColor = .white
        configuration.receiverTextColor = .lightGray
        // 创建礼物管理器
        giftManager = GiftQueManager<QueGift>()

        // 设置礼物停留时间
        giftManager?.stayDuration = 2.0
        
        // 设置礼物消失回调 - 注意使用 weakSelf
        giftManager?.giftDisappearedCallback = { [weak self] key, gift in
            // 不要使用 guard let self = self 这会创建强引用
            guard let weakSelf = self else { 
                TLog("🟢礼物消失回调 - self已被释放")
                return 
            }
            TLog("🟢礼物已消失: \(key), 数量: \(gift.newValue)")
            // 执行礼物消失后的逻辑
            if let msg = MsgRoomTextMsg.creatGiftEnd(gift.ext, count: gift.newValue), gift.sender?.int?.isIMMe == true {
                RoomManger.shared.sendRoomMsg(msg: msg)
                weakSelf.msgAddSub.onNext(msg)
            }
        }

        // 设置队列全部消失回调 - 注意使用 weakSelf
        giftManager?.allGiftsDisappearedCallback = { [weak self] in
            // 不要使用 guard let self = self 这会创建强引用
            guard let weakSelf = self else { 
                TLog("🟢所有礼物消失回调 - self已被释放")
                return 
            }
            TLog("🟢所有礼物已消失超过2秒")
            // 执行队列清空后的逻辑，例如隐藏容器视图等
            if RoomManger.shared.curretRole == .owner {
                RoomManger.shared.loadAllCount(isSend: true)
            }
        }
        
        // 设置礼物第一次回调 - 注意使用 weakSelf
        giftManager?.giftFirstReceivedCallback = { [weak self] (key, gift) in
            // 不要使用 guard let self = self 这会创建强引用
            guard let weakSelf = self else { 
                TLog("🟢礼物第一次接收回调 - self已被释放")
                return 
            }
            
            if LWUserManger.shared.giftEffectType == .showNone { return }
            
            if LWUserManger.shared.giftEffectType == .showAll {
                let a = AnimationElement()
                a.animationView = tempV
                a.url = gift.giftEastUrl
                a.imageUrl = gift.giftIcon
                PlayerManager.shared.addAnimationElement(a)
            }
            
            if LWUserManger.shared.giftEffectType == .showExpensive, gift.ext?.giftValue ?? 0 >= 500 {
                let a = AnimationElement()
                a.animationView = tempV
                a.url = gift.giftEastUrl
                a.imageUrl = gift.giftIcon
                PlayerManager.shared.addAnimationElement(a)
            }
            
            
        }
        
        // 设置父视图和视图配置 - 注意使用 weakSelf，避免闭包中创建强引用
        giftManager?.setupContainer(tempV) { [weak self] slotIndex in
            guard let weakSelf = self else { 
                TLog("🟢视图配置回调 - self已被释放")
                return GiftShowView<QueGift>() 
            }
            // 创建新的礼物视图
            let giftView = GiftShowView<QueGift>()

            // 添加到父视图
            tempV.addSubview(giftView)

            // 设置约束
            giftView.snp.makeConstraints { (make) in
                // 根据槽位索引设置位置
                let yOffset = 100 - slotIndex * 50 // 槽位0在底部，槽位1在中间，槽位2在顶部
                make.bottom.equalTo(tempV.snp.centerY).offset(yOffset)
                make.right.equalTo(tempV.snp.left)
                make.width.equalTo(giftView.default_width)
                make.height.equalTo(giftView.default_height)
            }
            return giftView
        }
      
    }
    
    func createGiftModel(roomMsg: RoomBaseMsgModel) -> QueGift {
        let model = QueGift()
        model.giftID = roomMsg.giftId?.int ?? 0

        model.newValue = 1
        let name = roomMsg.receiverSeats.compactMap({ return $0.userName })
        let ids = roomMsg.receiverSeats.compactMap({ return $0.userId })
        // 设置发送者和接收者
        model.sender = roomMsg.userId
        model.receiver = ids.joined(separator: ",")
        // 设置发送者和接收者昵称
        model.senderName = isValid(roomMsg.nickName) ? roomMsg.nickName : roomMsg.roomBaseMsg?.nickName
        model.avatar = isValid(roomMsg.photoUrl) ? roomMsg.photoUrl : roomMsg.roomBaseMsg?.faceURL
        model.giftIcon = roomMsg.giftPic
        model.giftEastUrl = roomMsg.effectUrl

        model.receiverName = name.joined(separator: ",")
        model.ext = roomMsg

        // 检查是否为暴击模式（通过 tl_ext 判断）
        let hurtValue = roomMsg.hurtValue
        if hurtValue > 0 {
            // 暴击模式：设置 tl_ext 和初始伤害值
            model.tl_ext = roomMsg
            model.totalHurtValue = hurtValue
            model.sender = roomMsg.liveRoomNo
            model.receiver = roomMsg.toImUserIdArry?.compactMap({ $0 }).joined(separator: ",")
            TLog("创建暴击礼物 - 伤害值: \(hurtValue)")
        } else {
            // 普通模式
            model.tl_ext = nil
            TLog("创建普通礼物 - 数量: \(model.newValue)")
        }

        return model
    }
    
    func creatSytemNotice() {
        let msg = RoomBaseMsgModel()
        msg.msgType = .systemLocal
        msg.txtContent = "绿色交友，文明聊天。严禁广告、色情、辱骂、涉政等违规内容。官方将24小时在线巡查！"
        msgAddSub.onNext(msg)
    }
    
    func creatRoomNotice(tt: String) {
        let msg = RoomBaseMsgModel()
        msg.msgType = .roomNotice
        msg.txtContent = tt
        msgAddSub.onNext(msg)
    }
    
    
    func reloadRoomDetailInfo(callBack: (() -> Void)?) {
        
        var dict: [String: Any] = [:]
        dict["liveRoomNo"] = RoomManger.shared.groupID ?? ""
        
        NetworkUtility.request(target: .roomInfo(dict), model: RoomDetailModel.self) { result in
            if result.isError { return }
            self.infoModel = result.model
            RoomManger.shared.roomID = self.infoModel?.roomid.string
            callBack?()
        }
        
    }
    
    ///陪伴信息接口
    func peiBanInfoGet() {
        if RoomManger.shared.curretRole != .owner { return }
        if snapshotRelay.value?.config.mode != RoomType.accompany.rawValue { return }
        
        NetworkUtility.request(target: .roomCompanyTask, model: RoomCompanyTaskModel.self) { result in
            if result.isError { return }
            self.taskRelay.accept(result.model)
        }
    }
    
    //获取房间红包信息
    func getRedBag() {
        let dict: [String: Any] = ["liveRoomNo": RoomManger.shared.groupID ?? ""]
        NetworkUtility.request(target: .roomRedBagList(dict), model: RedBagBaseModel.self, isList: true) { result in
            if result.isError { return }
            self.hotRedBag.accept(result.modelArr)
        }
    }
    
    //游客 退房检查
    @discardableResult
    func showAudienceExitRoomDialog( topTaskData: [RoomTaskModel],
          onTick: @escaping (_ content: String, _ reward: String) -> Void,
          onFinish: @escaping () -> Void
    ) -> Bool {
        // 1. 必要条件检查
        guard userGoRoomTime > 0, !topTaskData.isEmpty else {
            return false
        }
        
        // 2. 找到第一个未完成的任务 并且 sort 为 0
        guard let currentTask = topTaskData.first(where: { $0.status == .notFinished && $0.taskSort == 0 }) else {
            return false
        }
        
        // 3. 计算已用时间和剩余时间（单位：秒）
        let now = Date().timeIntervalSince1970
        let elapsed = now * 1000 - TimeInterval(userGoRoomTime) // 毫秒
        let totalMillis = Double(currentTask.taskStandardNum) * MINUTE * 1000
        - (Double(currentTask.taskCount) * MINUTE * 1000 + elapsed)
        guard totalMillis > 0 else {
            return false
        }
        var remainingSeconds = Int(totalMillis / 1000)
        
        // 4. 立即回调一次 onTick
        onTick("\(remainingSeconds)秒", currentTask.taskRewardRemark ?? "")
        
        // 5. 启动定时器，每秒回调一次
        
        TickHub.shared.register(
            name: TickJobName.exitRoomTask,
            interval: 1) {
                remainingSeconds -= 1
                if remainingSeconds == 0 {
                    TickHub.shared.unregister(name: TickJobName.exitRoomTask)
                    onFinish()
                    return
                }
                onTick("\(remainingSeconds)秒", currentTask.taskRewardRemark ?? "")
            }
        return true    
    }
    
    //获取在房间被别人挂上的技能
    func getUserSkillInfo() {
        let dict: [String: Any] = ["liveRoomNo": RoomManger.shared.groupID ?? ""]
        NetworkUtility.request(target: .roomSkillInfo(dict), model: RoomSkillUsestatus.self) { result in
            if result.isError { return }
            RoomManger.shared.skillUseInfo.accept(result.model)
        }
    }
    
    func getHuyanluanyu(callback: @escaping((String)->Void)) {
        NetworkUtility.request(target: .roomSkillText(["type": 0]), model: RoomSkillUsestatus.self) { result in
            if result.isError { return }
            if let text = result.dataJson?.rawString() {
                callback(text)
            }
        }
    }
    

    // 在 deinit 中确保清理
    deinit {
        // 检查 T_IMHelper 引用
        TLog("🌟deinit中 - T_IMHelper.shared.delegates 是否包含 self: \(T_IMHelper.shared.delegates.contains(where: { $0 === self }))")
        
        if giftManager != nil {
            giftManager?.allGiftsDisappearedCallback = nil
            giftManager?.giftFirstReceivedCallback   = nil
            giftManager?.giftDisappearedCallback     = nil
            giftManager = nil
        }
        // 尝试再次从 T_IMHelper 中移除自己
        T_IMHelper.shared.removeDelegate(self)
        TLog("🌟移除后 - T_IMHelper.shared.delegates 是否包含 self: \(T_IMHelper.shared.delegates.contains(where: { $0 === self }))")
        
        TLog("🌟房间管理服务 销毁完成")
    }
    
}


extension RoomViewModel: MessageHandlerDelegate {
    
    func groupNewMsg(dict: [String : RoomBaseMsgModel]) {
        //取自己直播间
        if let mm = dict[roomId] {
            
            //收到技能
            if mm.iMsgType == .skillPk {
                //被攻击的人不是我 就不弹
                if mm.otherUserImNumber?.isIMMe == false { return }
                
                //变猪头 或者 点穴手 刷新本地数据
                if mm.skillType == .skill3 || mm.skillType == .skill2 {
                    let vv = RoomManger.shared.skillUseInfo.value
                    if mm.skillType == .skill3 {
                        vv?.talkRubbishCount = mm.value ?? 0
                    } else {
                        vv?.shutUpEndTime = Int(Date.timestampByAdding(minutes: mm.value ?? 0)) * 1000
                    }
                    RoomManger.shared.skillUseInfo.accept(vv)
                    
                    //点穴手 不是房主 并且在麦上   就给他下麦
                    if mm.skillType == .skill2 && RoomManger.shared.curretRole != .owner && RoomManger.shared.isOnSeat {
                        RoomManger.shared.takeSeat(isUp: false, index: RoomManger.shared.curretIndex) { code, cc in
                        }
                    }
                }
                
                //旋风腿 收到就退出房间
                if mm.skillType == .skill1 {
                    self.exitRoomBehavir.accept(true)
                }
                
                let pk = PkResultPopView()
                pk.msgModel = mm
                pk.show()
                return
            }
            
            ///收到别人
            if mm.iMsgType == .giftCountSelfSync || mm.iMsgType == .giftCountBatchSync {
                RoomManger.shared.merge(with: mm.giftValues ?? [])
                RoomManger.shared.refeshGiftRelay.accept(true)
                return
            }
            
            //hot红包雨触发
            if mm.iMsgType == .htmlRichText && mm.text?.contains("gift_redbag") == true {
                getRedBag()
            }
            
            ///上Hot
            if mm.iMsgType == .roomHotState {
                var isHot = mm.typeCode == 0
                let time = Int(Date().timeIntervalSince1970) * 1000
                infoModel?.voiceLiveVo?.labelStartDate = time.string
                RoomManger.shared.updateHotInfo(time: time, isHot: isHot)
                return
            }
            
            ///收到连送礼物 5002
            if mm.iMsgType == .giftAnimation {
                let gift = createGiftModel(roomMsg: mm)
                let value = gift.ext?.giftValue ?? 0
                
                gift.ext?.receiverSeats.forEach({ [weak self] info in
                    guard let _ = self else { return }
                    let allV = Float((RoomManger.shared.diamonds(for: info.userId.int ?? 0) ?? 0) + value)
                    RoomManger.shared.update(imNumber: info.userId.int ?? 0, diamonds: allV)
                })
                RoomManger.shared.refeshGiftRelay.accept(true)
                giftManager?.receive(value: gift)
                return
            }
            
            ///右下角进房信息
            if mm.iMsgType == .joinRoomPopup {
                //房主统一发送进房欢迎
                if RoomManger.shared.curretRole == .owner {
                    var user = SeatInfo()
                    user.userId = mm.userId ?? ""
                    user.userName = mm.roomBaseMsg?.nickName
                    var msg = MsgRoomTextMsg(atUsers: [user], msgType: .welcome)
                    msg.txtContent = "欢迎 @\(user.userName ?? "") 来到房间"
                    RoomManger.shared.sendRoomMsg(msg: msg)
                    if let cModel = RoomBaseMsgModel.deserialize(from: msg.toDictionary()) {
                        msgAddSub.onNext(cModel)
                    }
                }
                return
            }
            
            if mm.iMsgType == .musicSwitch {
                MusicPlaybackManager.shared.stop()
                if RoomManger.shared.curretRole == .admin {
                    
                }
            }
            
            ///置空未读数
            if mm.iMsgType == .giftCountReset {
                RoomManger.shared.clear()
                RoomManger.shared.refeshGiftRelay.accept(true)
                return
            }
            
            //如果是踢出房间消息
            if mm.iMsgType == .kickRoom {
                
                if RoomManger.shared.curretRole != .audience {
                    msgAddSub.onNext(mm)
                }
                
                let arr = mm.atUsers.filter({ $0.userId == kuser.imNumber.string })
                if arr.count == 0 { return }
                
                let alertView = MessagePopupView(title: "温馨提示", content: "您被请出房间，3分钟内无法进入该直播间")
                alertView.show()
                
                if RoomFloatingWindowManager.shared.isFloatingWindowVisible {
                    RoomFloatingWindowManager.shared.cleanup()
                } else {
                    exitRoomBehavir.accept(true)
                }
                
                
                return
            }
            msgAddSub.onNext(mm)
        }
        
        //取公共群聊
        if var model = dict[EnvManager.shared.groupLiveID], !model.iMsgType.isOldFloat {
            
            //屠龙 攻击数值 推送
            if model.iMsgType == .tuLongHurtValue {
                tlMsgAddSub.onNext(model)
                
                if RoomManger.shared.groupID == model.liveRoomNo {
                    let gift = createGiftModel(roomMsg: model)
                    giftManager?.receive(value: gift)
                }
                
                return
            }
            
            //屠龙 红包雨触发
            if model.iMsgType == .tuLongRed {
                if isRoomVisible {
                    return
                }
                ///开始一轮红包雨 需要先清除一下历史记录
                redGift.removeAll()
                tlRedyAddSub.onNext(model.redEnvelopesId ?? "")
                return
            }
            
            // 屠龙 结算榜单
            if model.iMsgType == .tuLongJieS {
                if let a = TuLongSettlementModel.deserialize(from: model.content) {
                    let vv = TulongSettlementPopView()
                    vv.settlementData = a
                    vv.show()
                }
                return
            }
            
            //贵族红包
            if model.iMsgType == .kingRedPacket {
                getRedBag()
                return
            }
            
        
            
            msgAddSub.onNext(model)
            ///全服 boss 宝箱开启
            if model.iMsgType == .accountLevelUp {
                
            }
            
        }
    }
    
}


struct RoomCompanyTaskModel: SmartCodable {
    var days: String?
    var jsonArray = [RoomTextModel]()
    // 0不可领取  1 可领取 2 已领取
    var status: Int = 0
}

struct RoomTextModel: SmartCodable {
    var text: String?
}

class RoomSkillUsestatus: SmartCodable {
    var shutUpEndTime: Int = 0
    var talkRubbishCount: Int = 0
    required init() {
        
    }
    
    ///判断是否被点穴手禁言中
    func isAllow() -> Bool {
        let shutUpStamp = shutUpEndTime
        let nowStamp = Date.nowDate().milliStamp
        if shutUpStamp - nowStamp > 0 {
            return true
        }
        return false
    }
    ///获取禁言剩余时间
    func getRemainingMuteTimeStr()->String{
        let shutUpStamp = shutUpEndTime
        let nowStamp = Date.nowDate().milliStamp
        let second = (shutUpStamp - nowStamp)/1000
        let minutes = convertSecondsToMinutes(second)
        return "你已被点穴手击中，禁言\(minutes)分钟"
    }
    
    func convertSecondsToMinutes(_ seconds: Int) -> Int {
        let minutes = (seconds + 59) / 60 // 对总秒数向上取整
        return max(minutes, 1) // 小于60秒算1分钟
    }
}
