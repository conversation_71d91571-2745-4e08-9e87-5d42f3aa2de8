//
//  ScreenTestViewController.swift
//  YINDONG
//
//  Created by jj on 2025/6/27.
//

import UIKit

class ScreenTestViewController: UIViewController {
    
    // MARK: - UI Elements
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .systemBackground
        return scrollView
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "飘屏管理系统测试"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textAlignment = .center
        label.textColor = .label
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupScreenManager()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "飘屏测试"
        
        // 添加导航栏按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "清空队列",
            style: .plain,
            target: self,
            action: #selector(clearQueueTapped)
        )
        
        // 设置布局
        view.addSubview(scrollView)
        scrollView.addSubview(stackView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
            make.width.equalTo(scrollView).offset(-40)
        }
        
        // 添加UI元素
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(createSectionView(title: "滑动动画测试", buttons: [
            ("红色滑动", #selector(addRedSlideFloating)),
            ("蓝色滑动", #selector(addBlueSlideFloating)),
            ("绿色滑动", #selector(addGreenSlideFloating))
        ]))
        
        stackView.addArrangedSubview(createSectionView(title: "渐变动画测试", buttons: [
            ("橙色渐变", #selector(addOrangeFadeFloating)),
            ("紫色渐变", #selector(addPurpleFadeFloating)),
            ("粉色渐变", #selector(addPinkFadeFloating))
        ]))
        
        stackView.addArrangedSubview(createSectionView(title: "批量测试", buttons: [
            ("添加所有测试色块", #selector(addAllTestBlocks)),
            ("连续添加5个", #selector(addMultipleBlocks))
        ]))
        
        stackView.addArrangedSubview(createSectionView(title: "控制操作", buttons: [
            ("停止当前", #selector(stopCurrentFloating)),
            ("清空队列", #selector(clearQueueTapped))
        ]))
    }
    
    private func setupScreenManager() {
        // 设置飘屏管理器的容器为当前视图
        ScreenManagement.shared.setContainer(view)
    }
    
    private func createSectionView(title: String, buttons: [(String, Selector)]) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .secondarySystemBackground
        sectionView.layer.cornerRadius = 12
        sectionView.layer.masksToBounds = true
        
        let sectionStackView = UIStackView()
        sectionStackView.axis = .vertical
        sectionStackView.spacing = 12
        sectionStackView.alignment = .fill
        sectionStackView.distribution = .fill
        
        // 添加标题
        let sectionTitleLabel = UILabel()
        sectionTitleLabel.text = title
        sectionTitleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        sectionTitleLabel.textColor = .label
        sectionStackView.addArrangedSubview(sectionTitleLabel)
        
        // 添加按钮
        let buttonStackView = UIStackView()
        buttonStackView.axis = .vertical
        buttonStackView.spacing = 8
        buttonStackView.alignment = .fill
        buttonStackView.distribution = .fillEqually
        
        for (buttonTitle, action) in buttons {
            let button = UIButton(type: .system)
            button.setTitle(buttonTitle, for: .normal)
            button.backgroundColor = .systemBlue
            button.setTitleColor(.white, for: .normal)
            button.layer.cornerRadius = 8
            button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            button.addTarget(self, action: action, for: .touchUpInside)
            
            button.snp.makeConstraints { make in
                make.height.equalTo(44)
            }
            
            buttonStackView.addArrangedSubview(button)
        }
        
        sectionStackView.addArrangedSubview(buttonStackView)
        
        sectionView.addSubview(sectionStackView)
        sectionStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        return sectionView
    }
}

// MARK: - Actions
extension ScreenTestViewController {
    
    @objc private func addRedSlideFloating() {
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemRed,
            position: .top,
            animationType: .slideFromRight,
            stayDuration: 2.0
        )
    }

    @objc private func addBlueSlideFloating() {
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemBlue,
            position: .center,
            animationType: .slideFromLeft,
            stayDuration: 3.0
        )
    }

    @objc private func addGreenSlideFloating() {
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemGreen,
            position: .bottom,
            animationType: .slideFromTop,
            stayDuration: 4.0
        )
    }

    @objc private func addOrangeFadeFloating() {
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemOrange,
            position: .center,
            animationType: .fadeInOut,
            stayDuration: 2.5
        )
    }

    @objc private func addPurpleFadeFloating() {
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemPurple,
            position: .topRight,
            animationType: .scaleInOut,
            stayDuration: 3.5
        )
    }

    @objc private func addPinkFadeFloating() {
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemPink,
            position: .bottomLeft,
            animationType: .slideFromBottom,
            stayDuration: 2.0
        )
    }
    
    @objc private func addAllTestBlocks() {
        ScreenManagement.shared.addTestColorBlocks()
    }
    
    @objc private func addMultipleBlocks() {
        let colors: [UIColor] = [.red, .blue, .green, .orange, .purple]
        let positions: [ScreenFloatingPosition] = [.top, .center, .bottom, .topLeft, .topRight]
        let animations: [ScreenFloatingAnimationType] = [.slideFromRight, .fadeInOut, .scaleInOut, .slideFromLeft, .slideFromBottom]

        for (index, color) in colors.enumerated() {
            ScreenManagement.shared.addColorBlockFloating(
                color: color,
                position: positions[index % positions.count],
                animationType: animations[index % animations.count],
                stayDuration: TimeInterval(2 + index)
            )
        }
    }
    
    @objc private func stopCurrentFloating() {
        ScreenManagement.shared.stopCurrent()
    }
    
    @objc private func clearQueueTapped() {
        ScreenManagement.shared.clearQueue()
    }
}
