//
//  ScreenManagement.swift
//  YINDONG
//
//  Created by jj on 2025/6/27.
//

import UIKit
import SVGAPlayer
import Lottie

// MARK: - 飘屏类型枚举
enum ScreenFloatingType {
    case slideFromRight    // 从右边滑入到左边滑出
    case fadeInCenter      // 中间渐变显示
    case custom           // 自定义动画
}

// MARK: - 飘屏动画类型
enum ScreenAnimationType {
    case svga(String)     // SVGA动画，参数为资源名称
    case lottie(String)   // Lottie动画，参数为资源名称
    case colorBlock(UIColor) // 色块测试，参数为颜色
}

// MARK: - 飘屏配置模型
struct ScreenFloatingConfig {
    let type: ScreenFloatingType
    let animationType: ScreenAnimationType
    let stayDuration: TimeInterval  // 停留时间
    let enterDuration: TimeInterval // 进场动画时长
    let exitDuration: TimeInterval  // 出场动画时长
    let size: CGSize               // 飘屏尺寸

    init(type: ScreenFloatingType = .slideFromRight,
         animationType: ScreenAnimationType,
         stayDuration: TimeInterval = 3.0,
         enterDuration: TimeInterval = 1.0,
         exitDuration: TimeInterval = 1.0,
         size: CGSize = CGSize(width: AppTool.screenWidth, height: 100)) {
        self.type = type
        self.animationType = animationType
        self.stayDuration = stayDuration
        self.enterDuration = enterDuration
        self.exitDuration = exitDuration
        self.size = size
    }
}

// MARK: - 飘屏项目
class ScreenFloatingItem {
    let config: ScreenFloatingConfig
    let id: String
    var view: UIView?
    var isPlaying: Bool = false

    init(config: ScreenFloatingConfig, id: String = UUID().uuidString) {
        self.config = config
        self.id = id
    }
}

//全局飘屏管理类
class ScreenManagement: NSObject {

    // MARK: - 单例
    static let shared = ScreenManagement()
    private override init() {
        super.init()
    }

    // MARK: - 私有属性
    private var floatingQueue: [ScreenFloatingItem] = []
    private var currentItem: ScreenFloatingItem?
    private var containerView: UIView?
    private let serialQueue = DispatchQueue(label: "screen.floating.queue")

    // MARK: - 公共方法

    /// 设置容器视图（通常是主窗口）
    func setContainer(_ container: UIView) {
        containerView = container
    }

    /// 添加飘屏到队列
    func addFloatingScreen(config: ScreenFloatingConfig) {
        let item = ScreenFloatingItem(config: config)
        serialQueue.async {
            self.floatingQueue.append(item)
            self.processNextIfNeeded()
        }
    }

    /// 清空队列
    func clearQueue() {
        serialQueue.async {
            self.floatingQueue.removeAll()
        }
    }

    /// 停止当前播放
    func stopCurrent() {
        serialQueue.async {
            if let current = self.currentItem {
                self.removeFloatingView(current)
                self.currentItem = nil
                self.processNextIfNeeded()
            }
        }
    }
}

// MARK: - 私有方法
extension ScreenManagement {

    /// 处理下一个飘屏（如果需要）
    private func processNextIfNeeded() {
        guard currentItem == nil, !floatingQueue.isEmpty else { return }

        let nextItem = floatingQueue.removeFirst()
        currentItem = nextItem

        DispatchQueue.main.async {
            self.showFloatingScreen(nextItem)
        }
    }

    /// 显示飘屏
    private func showFloatingScreen(_ item: ScreenFloatingItem) {
        guard let container = containerView else {
            print("ScreenManagement: 容器视图未设置")
            serialQueue.async {
                self.currentItem = nil
                self.processNextIfNeeded()
            }
            return
        }

        item.isPlaying = true

        // 创建飘屏视图
        let floatingView = createFloatingView(for: item)
        item.view = floatingView

        // 添加到容器
        container.addSubview(floatingView)

        // 执行进场动画
        performEnterAnimation(for: item) {
            // 进场动画完成后，开始停留计时
            self.startStayTimer(for: item)
        }
    }

    /// 创建飘屏视图
    private func createFloatingView(for item: ScreenFloatingItem) -> UIView {
        let config = item.config
        let containerView = UIView(frame: CGRect(origin: .zero, size: config.size))
        containerView.backgroundColor = .clear

        switch config.animationType {
        case .svga(let resourceName):
            let svgaView = createSVGAView(resourceName: resourceName, size: config.size)
            containerView.addSubview(svgaView)
            svgaView.frame = containerView.bounds

        case .lottie(let resourceName):
            let lottieView = createLottieView(resourceName: resourceName, size: config.size)
            containerView.addSubview(lottieView)
            lottieView.frame = containerView.bounds

        case .colorBlock(let color):
            let colorView = createColorBlockView(color: color, size: config.size)
            containerView.addSubview(colorView)
            colorView.frame = containerView.bounds
        }

        return containerView
    }

    /// 创建SVGA视图
    private func createSVGAView(resourceName: String, size: CGSize) -> SVGAnimationPlayer {
        let svgaView = SVGAnimationPlayer()
        svgaView.frame = CGRect(origin: .zero, size: size)
        svgaView.contentMode = .scaleAspectFit
        svgaView.loops = 0 // 无限循环
        svgaView.resourceName = resourceName
        return svgaView
    }

    /// 创建Lottie视图
    private func createLottieView(resourceName: String, size: CGSize) -> LottieAnimationView {
        let lottieView = LottieAnimationView()
        lottieView.frame = CGRect(origin: .zero, size: size)
        lottieView.contentMode = .scaleAspectFit
        lottieView.loopMode = .loop
        lottieView.animation = LottieAnimation.named(resourceName)
        lottieView.play()
        return lottieView
    }

    /// 创建色块视图（用于测试）
    private func createColorBlockView(color: UIColor, size: CGSize) -> UIView {
        let colorView = UIView(frame: CGRect(origin: .zero, size: size))
        colorView.backgroundColor = color
        colorView.layer.cornerRadius = 8
        colorView.layer.masksToBounds = true

        // 添加一个标签显示颜色信息
        let label = UILabel()
        label.text = "测试色块"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textAlignment = .center
        colorView.addSubview(label)
        label.frame = colorView.bounds

        return colorView
    }

    /// 执行进场动画
    private func performEnterAnimation(for item: ScreenFloatingItem, completion: @escaping () -> Void) {
        guard let floatingView = item.view,
              let container = containerView else {
            completion()
            return
        }

        let config = item.config
        let containerBounds = container.bounds

        switch config.type {
        case .slideFromRight:
            // 从屏幕右边滑入到左边
            let startX = containerBounds.width
            let endX = containerBounds.width - config.size.width - 20 // 距离右边20点
            let centerY = containerBounds.height / 2 - config.size.height / 2

            // 设置初始位置（屏幕外右边）
            floatingView.frame = CGRect(x: startX, y: centerY, width: config.size.width, height: config.size.height)

            // 执行滑入动画
            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseOut],
                          animations: {
                floatingView.frame.origin.x = endX
            }, completion: { _ in
                completion()
            })

        case .fadeInCenter:
            // 中间渐变显示
            let centerX = containerBounds.width / 2 - config.size.width / 2
            let centerY = containerBounds.height / 2 - config.size.height / 2

            floatingView.frame = CGRect(x: centerX, y: centerY, width: config.size.width, height: config.size.height)
            floatingView.alpha = 0

            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseInOut],
                          animations: {
                floatingView.alpha = 1.0
            }, completion: { _ in
                completion()
            })

        case .custom:
            // 自定义动画，这里可以根据需要扩展
            let centerX = containerBounds.width / 2 - config.size.width / 2
            let centerY = containerBounds.height / 2 - config.size.height / 2
            floatingView.frame = CGRect(x: centerX, y: centerY, width: config.size.width, height: config.size.height)
            completion()
        }
    }

    /// 开始停留计时
    private func startStayTimer(for item: ScreenFloatingItem) {
        DispatchQueue.main.asyncAfter(deadline: .now() + item.config.stayDuration) {
            self.performExitAnimation(for: item) {
                self.serialQueue.async {
                    self.removeFloatingView(item)
                    self.currentItem = nil
                    self.processNextIfNeeded()
                }
            }
        }
    }

    /// 执行出场动画
    private func performExitAnimation(for item: ScreenFloatingItem, completion: @escaping () -> Void) {
        guard let floatingView = item.view,
              let container = containerView else {
            completion()
            return
        }

        let config = item.config
        let containerBounds = container.bounds

        switch config.type {
        case .slideFromRight:
            // 继续向左滑出屏幕
            let endX = -config.size.width

            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseIn],
                          animations: {
                floatingView.frame.origin.x = endX
            }, completion: { _ in
                completion()
            })

        case .fadeInCenter:
            // 渐变消失
            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseInOut],
                          animations: {
                floatingView.alpha = 0
            }, completion: { _ in
                completion()
            })

        case .custom:
            // 自定义退场动画
            completion()
        }
    }

    /// 移除飘屏视图
    private func removeFloatingView(_ item: ScreenFloatingItem) {
        DispatchQueue.main.async {
            item.view?.removeFromSuperview()
            item.view = nil
            item.isPlaying = false
        }
    }
}

// MARK: - 便捷方法
extension ScreenManagement {

    /// 添加SVGA飘屏
    func addSVGAFloating(resourceName: String,
                        type: ScreenFloatingType = .slideFromRight,
                        stayDuration: TimeInterval = 3.0,
                        size: CGSize = CGSize(width: 200, height: 100)) {
        let config = ScreenFloatingConfig(
            type: type,
            animationType: .svga(resourceName),
            stayDuration: stayDuration,
            size: size
        )
        addFloatingScreen(config: config)
    }

    /// 添加Lottie飘屏
    func addLottieFloating(resourceName: String,
                          type: ScreenFloatingType = .slideFromRight,
                          stayDuration: TimeInterval = 3.0,
                          size: CGSize = CGSize(width: 200, height: 100)) {
        let config = ScreenFloatingConfig(
            type: type,
            animationType: .lottie(resourceName),
            stayDuration: stayDuration,
            size: size
        )
        addFloatingScreen(config: config)
    }

    /// 添加色块飘屏（用于测试）
    func addColorBlockFloating(color: UIColor,
                              type: ScreenFloatingType = .slideFromRight,
                              stayDuration: TimeInterval = 3.0,
                               size: CGSize = CGSize(width: AppTool.screenWidth, height: 100)) {
        let config = ScreenFloatingConfig(
            type: type,
            animationType: .colorBlock(color),
            stayDuration: stayDuration,
            size: size
        )
        addFloatingScreen(config: config)
    }

    /// 批量添加测试色块
    func addTestColorBlocks() {
        let colors: [UIColor] = [.red, .blue, .green, .orange, .purple, .systemPink]
        let types: [ScreenFloatingType] = [.slideFromRight, .fadeInCenter]

        for (index, color) in colors.enumerated() {
            let type = types[index % types.count]
            let stayDuration = TimeInterval(2 + index) // 不同的停留时间
            addColorBlockFloating(color: color, type: type, stayDuration: stayDuration)
        }
    }
}

// MARK: - 使用示例
/*
 使用示例：

 // 1. 设置容器视图（通常在AppDelegate或主窗口设置）
 ScreenManagement.shared.setContainer(UIApplication.shared.windows.first!)

 // 2. 添加SVGA飘屏
 ScreenManagement.shared.addSVGAFloating(
     resourceName: "gift_animation",
     type: .slideFromRight,
     stayDuration: 4.0,
     size: CGSize(width: 250, height: 120)
 )

 // 3. 添加Lottie飘屏
 ScreenManagement.shared.addLottieFloating(
     resourceName: "celebration",
     type: .fadeInCenter,
     stayDuration: 3.0
 )

 // 4. 添加测试色块
 ScreenManagement.shared.addTestColorBlocks()

 // 5. 清空队列
 ScreenManagement.shared.clearQueue()

 // 6. 停止当前播放
 ScreenManagement.shared.stopCurrent()
 */
