//
//  ScreenManagement.swift
//  YINDONG
//
//  Created by jj on 2025/6/27.
//

import UIKit
import SVGAPlayer
import Lottie

// MARK: - 飘屏位置配置枚举
enum ScreenFloatingPosition {
    case top              // 顶部，全屏宽度
    case center           // 中间居中
    case bottom           // 底部，全屏宽度
    case topLeft          // 左上角
    case topRight         // 右上角
    case bottomLeft       // 左下角
    case bottomRight      // 右下角
    case custom(x: CGFloat, y: CGFloat, width: CGFloat, height: CGFloat) // 完全自定义

    /// 根据位置获取默认frame
    func getFrame(containerSize: CGSize) -> CGRect {
        switch self {
        case .top:
            return CGRect(x: 0, y: 0, width: containerSize.width, height: 100)
        case .center:
            return CGRect(x: (containerSize.width - 200) / 2,
                         y: (containerSize.height - 100) / 2,
                         width: 200, height: 100)
        case .bottom:
            return CGRect(x: 0, y: containerSize.height - 100,
                         width: containerSize.width, height: 100)
        case .topLeft:
            return CGRect(x: 20, y: 44, width: 150, height: 80)
        case .topRight:
            return CGRect(x: containerSize.width - 170, y: 44, width: 150, height: 80)
        case .bottomLeft:
            return CGRect(x: 20, y: containerSize.height - 124, width: 150, height: 80)
        case .bottomRight:
            return CGRect(x: containerSize.width - 170,
                         y: containerSize.height - 124, width: 150, height: 80)
        case .custom(let x, let y, let width, let height):
            return CGRect(x: x, y: y, width: width, height: height)
        }
    }
}

// MARK: - 飘屏动画类型枚举
enum ScreenFloatingAnimationType {
    case slideFromRight    // 从右边滑入到左边滑出
    case slideFromLeft     // 从左边滑入到右边滑出
    case slideFromTop      // 从上边滑入到下边滑出
    case slideFromBottom   // 从下边滑入到上边滑出
    case fadeInOut         // 渐变进出
    case scaleInOut        // 缩放进出
    case none              // 无动画，直接显示隐藏
}

// MARK: - 飘屏内容类型
enum ScreenFloatingContentType {
    case svga(String)      // SVGA动画，参数为资源名称
    case lottie(String)    // Lottie动画，参数为资源名称
    case colorBlock(UIColor) // 色块测试，参数为颜色
    case customView(() -> UIView) // 自定义视图创建闭包
}

// MARK: - 飘屏配置模型
struct ScreenFloatingConfig {
    let position: ScreenFloatingPosition           // 位置配置
    let animationType: ScreenFloatingAnimationType // 动画类型
    let contentType: ScreenFloatingContentType     // 内容类型
    let stayDuration: TimeInterval                 // 停留时间
    let enterDuration: TimeInterval                // 进场动画时长
    let exitDuration: TimeInterval                 // 出场动画时长

    init(position: ScreenFloatingPosition,
         animationType: ScreenFloatingAnimationType = .slideFromRight,
         contentType: ScreenFloatingContentType,
         stayDuration: TimeInterval = 3.0,
         enterDuration: TimeInterval = 1.0,
         exitDuration: TimeInterval = 1.0) {
        self.position = position
        self.animationType = animationType
        self.contentType = contentType
        self.stayDuration = stayDuration
        self.enterDuration = enterDuration
        self.exitDuration = exitDuration
    }
}

// MARK: - 飘屏项目
class ScreenFloatingItem {
    let config: ScreenFloatingConfig
    let id: String
    var view: UIView?
    var isPlaying: Bool = false

    init(config: ScreenFloatingConfig, id: String = UUID().uuidString) {
        self.config = config
        self.id = id
    }
}

//全局飘屏管理类
class ScreenManagement: NSObject {

    // MARK: - 单例
    static let shared = ScreenManagement()
    private override init() {
        super.init()
    }

    // MARK: - 私有属性
    private var floatingQueue: [ScreenFloatingItem] = []
    private var currentItem: ScreenFloatingItem?
    private var containerView: UIView?
    private let serialQueue = DispatchQueue(label: "screen.floating.queue")

    // MARK: - 公共方法

    /// 设置容器视图（通常是主窗口）
    func setContainer(_ container: UIView) {
        containerView = container
    }

    /// 添加飘屏到队列
    func addFloatingScreen(config: ScreenFloatingConfig) {
        let item = ScreenFloatingItem(config: config)
        serialQueue.async {
            self.floatingQueue.append(item)
            self.processNextIfNeeded()
        }
    }

    /// 清空队列
    func clearQueue() {
        serialQueue.async {
            self.floatingQueue.removeAll()
        }
    }

    /// 停止当前播放
    func stopCurrent() {
        serialQueue.async {
            if let current = self.currentItem {
                self.removeFloatingView(current)
                self.currentItem = nil
                self.processNextIfNeeded()
            }
        }
    }
}

// MARK: - 私有方法
extension ScreenManagement {

    /// 处理下一个飘屏（如果需要）
    private func processNextIfNeeded() {
        guard currentItem == nil, !floatingQueue.isEmpty else { return }

        let nextItem = floatingQueue.removeFirst()
        currentItem = nextItem

        DispatchQueue.main.async {
            self.showFloatingScreen(nextItem)
        }
    }

    /// 显示飘屏
    private func showFloatingScreen(_ item: ScreenFloatingItem) {
        guard let container = containerView else {
            print("ScreenManagement: 容器视图未设置")
            serialQueue.async {
                self.currentItem = nil
                self.processNextIfNeeded()
            }
            return
        }

        item.isPlaying = true

        // 创建飘屏视图
        let floatingView = createFloatingView(for: item)
        item.view = floatingView

        // 添加到容器
        container.addSubview(floatingView)

        // 执行进场动画
        performEnterAnimation(for: item) {
            // 进场动画完成后，开始停留计时
            self.startStayTimer(for: item)
        }
    }

    /// 创建飘屏视图
    private func createFloatingView(for item: ScreenFloatingItem) -> UIView {
        let config = item.config
        guard let container = containerView else {
            return UIView()
        }

        // 根据配置获取frame
        let frame = config.position.getFrame(containerSize: container.bounds.size)
        let floatingView = UIView(frame: frame)
        floatingView.backgroundColor = .clear

        // 根据内容类型创建内容视图
        let contentView: UIView
        switch config.contentType {
        case .svga(let resourceName):
            contentView = createSVGAView(resourceName: resourceName, size: frame.size)

        case .lottie(let resourceName):
            contentView = createLottieView(resourceName: resourceName, size: frame.size)

        case .colorBlock(let color):
            contentView = createColorBlockView(color: color, size: frame.size)

        case .customView(let viewCreator):
            contentView = viewCreator()
            contentView.frame = CGRect(origin: .zero, size: frame.size)
        }

        floatingView.addSubview(contentView)
        contentView.frame = floatingView.bounds

        return floatingView
    }

    /// 创建SVGA视图
    private func createSVGAView(resourceName: String, size: CGSize) -> SVGAnimationPlayer {
        let svgaView = SVGAnimationPlayer()
        svgaView.frame = CGRect(origin: .zero, size: size)
        svgaView.contentMode = .scaleAspectFit
        svgaView.loops = 0 // 无限循环
        svgaView.resourceName = resourceName
        return svgaView
    }

    /// 创建Lottie视图
    private func createLottieView(resourceName: String, size: CGSize) -> LottieAnimationView {
        let lottieView = LottieAnimationView()
        lottieView.frame = CGRect(origin: .zero, size: size)
        lottieView.contentMode = .scaleAspectFit
        lottieView.loopMode = .loop
        lottieView.animation = LottieAnimation.named(resourceName)
        lottieView.play()
        return lottieView
    }

    /// 创建色块视图（用于测试）
    private func createColorBlockView(color: UIColor, size: CGSize) -> UIView {
        let colorView = UIView(frame: CGRect(origin: .zero, size: size))
        colorView.backgroundColor = color
        colorView.layer.cornerRadius = 8
        colorView.layer.masksToBounds = true

        // 添加一个标签显示颜色信息
        let label = UILabel()
        label.text = "测试色块"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textAlignment = .center
        colorView.addSubview(label)
        label.frame = colorView.bounds

        return colorView
    }

    /// 执行进场动画
    private func performEnterAnimation(for item: ScreenFloatingItem, completion: @escaping () -> Void) {
        guard let floatingView = item.view,
              let container = containerView else {
            completion()
            return
        }

        let config = item.config
        let containerBounds = container.bounds
        let finalFrame = floatingView.frame // 最终位置已经在创建时设置好了

        switch config.animationType {
        case .slideFromRight:
            // 从屏幕右边滑入
            let startFrame = CGRect(x: containerBounds.width,
                                   y: finalFrame.origin.y,
                                   width: finalFrame.width,
                                   height: finalFrame.height)
            floatingView.frame = startFrame

            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseOut],
                          animations: {
                floatingView.frame = finalFrame
            }, completion: { _ in
                completion()
            })

        case .slideFromLeft:
            // 从屏幕左边滑入
            let startFrame = CGRect(x: -finalFrame.width,
                                   y: finalFrame.origin.y,
                                   width: finalFrame.width,
                                   height: finalFrame.height)
            floatingView.frame = startFrame

            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseOut],
                          animations: {
                floatingView.frame = finalFrame
            }, completion: { _ in
                completion()
            })

        case .slideFromTop:
            // 从屏幕上边滑入
            let startFrame = CGRect(x: finalFrame.origin.x,
                                   y: -finalFrame.height,
                                   width: finalFrame.width,
                                   height: finalFrame.height)
            floatingView.frame = startFrame

            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseOut],
                          animations: {
                floatingView.frame = finalFrame
            }, completion: { _ in
                completion()
            })

        case .slideFromBottom:
            // 从屏幕下边滑入
            let startFrame = CGRect(x: finalFrame.origin.x,
                                   y: containerBounds.height,
                                   width: finalFrame.width,
                                   height: finalFrame.height)
            floatingView.frame = startFrame

            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseOut],
                          animations: {
                floatingView.frame = finalFrame
            }, completion: { _ in
                completion()
            })

        case .fadeInOut:
            // 渐变显示
            floatingView.alpha = 0
            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseInOut],
                          animations: {
                floatingView.alpha = 1.0
            }, completion: { _ in
                completion()
            })

        case .scaleInOut:
            // 缩放显示
            floatingView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            UIView.animate(withDuration: config.enterDuration,
                          delay: 0,
                          options: [.curveEaseOut],
                          animations: {
                floatingView.transform = .identity
            }, completion: { _ in
                completion()
            })

        case .none:
            // 无动画，直接显示
            completion()
        }
    }
    }

    /// 开始停留计时
    private func startStayTimer(for item: ScreenFloatingItem) {
        DispatchQueue.main.asyncAfter(deadline: .now() + item.config.stayDuration) {
            self.performExitAnimation(for: item) {
                self.serialQueue.async {
                    self.removeFloatingView(item)
                    self.currentItem = nil
                    self.processNextIfNeeded()
                }
            }
        }
    }

    /// 执行出场动画
    private func performExitAnimation(for item: ScreenFloatingItem, completion: @escaping () -> Void) {
        guard let floatingView = item.view,
              let container = containerView else {
            completion()
            return
        }

        let config = item.config
        let containerBounds = container.bounds
        let currentFrame = floatingView.frame

        switch config.animationType {
        case .slideFromRight:
            // 继续向左滑出屏幕
            let endFrame = CGRect(x: -currentFrame.width,
                                 y: currentFrame.origin.y,
                                 width: currentFrame.width,
                                 height: currentFrame.height)

            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseIn],
                          animations: {
                floatingView.frame = endFrame
            }, completion: { _ in
                completion()
            })

        case .slideFromLeft:
            // 向右滑出屏幕
            let endFrame = CGRect(x: containerBounds.width,
                                 y: currentFrame.origin.y,
                                 width: currentFrame.width,
                                 height: currentFrame.height)

            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseIn],
                          animations: {
                floatingView.frame = endFrame
            }, completion: { _ in
                completion()
            })

        case .slideFromTop:
            // 向下滑出屏幕
            let endFrame = CGRect(x: currentFrame.origin.x,
                                 y: containerBounds.height,
                                 width: currentFrame.width,
                                 height: currentFrame.height)

            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseIn],
                          animations: {
                floatingView.frame = endFrame
            }, completion: { _ in
                completion()
            })

        case .slideFromBottom:
            // 向上滑出屏幕
            let endFrame = CGRect(x: currentFrame.origin.x,
                                 y: -currentFrame.height,
                                 width: currentFrame.width,
                                 height: currentFrame.height)

            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseIn],
                          animations: {
                floatingView.frame = endFrame
            }, completion: { _ in
                completion()
            })

        case .fadeInOut:
            // 渐变消失
            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseInOut],
                          animations: {
                floatingView.alpha = 0
            }, completion: { _ in
                completion()
            })

        case .scaleInOut:
            // 缩放消失
            UIView.animate(withDuration: config.exitDuration,
                          delay: 0,
                          options: [.curveEaseIn],
                          animations: {
                floatingView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            }, completion: { _ in
                completion()
            })

        case .none:
            // 无动画，直接隐藏
            completion()
        }
    }

    /// 移除飘屏视图
    private func removeFloatingView(_ item: ScreenFloatingItem) {
        DispatchQueue.main.async {
            item.view?.removeFromSuperview()
            item.view = nil
            item.isPlaying = false
        }
    }
}

// MARK: - 便捷方法
extension ScreenManagement {

    /// 添加SVGA飘屏
    func addSVGAFloating(resourceName: String,
                        position: ScreenFloatingPosition = .center,
                        animationType: ScreenFloatingAnimationType = .slideFromRight,
                        stayDuration: TimeInterval = 3.0) {
        let config = ScreenFloatingConfig(
            position: position,
            animationType: animationType,
            contentType: .svga(resourceName),
            stayDuration: stayDuration
        )
        addFloatingScreen(config: config)
    }

    /// 添加Lottie飘屏
    func addLottieFloating(resourceName: String,
                          position: ScreenFloatingPosition = .center,
                          animationType: ScreenFloatingAnimationType = .slideFromRight,
                          stayDuration: TimeInterval = 3.0) {
        let config = ScreenFloatingConfig(
            position: position,
            animationType: animationType,
            contentType: .lottie(resourceName),
            stayDuration: stayDuration
        )
        addFloatingScreen(config: config)
    }

    /// 添加色块飘屏（用于测试）
    func addColorBlockFloating(color: UIColor,
                              position: ScreenFloatingPosition = .top,
                              animationType: ScreenFloatingAnimationType = .slideFromRight,
                              stayDuration: TimeInterval = 3.0) {
        let config = ScreenFloatingConfig(
            position: position,
            animationType: animationType,
            contentType: .colorBlock(color),
            stayDuration: stayDuration
        )
        addFloatingScreen(config: config)
    }

    /// 添加自定义视图飘屏
    func addCustomViewFloating(viewCreator: @escaping () -> UIView,
                              position: ScreenFloatingPosition = .center,
                              animationType: ScreenFloatingAnimationType = .fadeInOut,
                              stayDuration: TimeInterval = 3.0) {
        let config = ScreenFloatingConfig(
            position: position,
            animationType: animationType,
            contentType: .customView(viewCreator),
            stayDuration: stayDuration
        )
        addFloatingScreen(config: config)
    }

    /// 批量添加测试色块
    func addTestColorBlocks() {
        let colors: [UIColor] = [.red, .blue, .green, .orange, .purple, .systemPink]
        let positions: [ScreenFloatingPosition] = [.top, .center, .bottom, .topLeft, .topRight, .bottomLeft]
        let animations: [ScreenFloatingAnimationType] = [.slideFromRight, .slideFromLeft, .fadeInOut, .scaleInOut, .slideFromTop, .slideFromBottom]

        for (index, color) in colors.enumerated() {
            let position = positions[index % positions.count]
            let animation = animations[index % animations.count]
            let stayDuration = TimeInterval(2 + index) // 不同的停留时间
            addColorBlockFloating(color: color, position: position, animationType: animation, stayDuration: stayDuration)
        }
    }
}

// MARK: - 使用示例
/*
 使用示例：

 // 1. 设置容器视图（通常在AppDelegate或主窗口设置）
 ScreenManagement.shared.setContainer(UIApplication.shared.windows.first!)

 // 2. 添加顶部SVGA飘屏
 ScreenManagement.shared.addSVGAFloating(
     resourceName: "gift_animation",
     position: .top,
     animationType: .slideFromRight,
     stayDuration: 4.0
 )

 // 3. 添加中间Lottie飘屏
 ScreenManagement.shared.addLottieFloating(
     resourceName: "celebration",
     position: .center,
     animationType: .fadeInOut,
     stayDuration: 3.0
 )

 // 4. 添加自定义位置色块
 ScreenManagement.shared.addColorBlockFloating(
     color: .systemBlue,
     position: .custom(x: 50, y: 100, width: 300, height: 80),
     animationType: .scaleInOut,
     stayDuration: 2.0
 )

 // 5. 添加自定义视图
 ScreenManagement.shared.addCustomViewFloating(
     viewCreator: {
         let label = UILabel()
         label.text = "自定义飘屏"
         label.textColor = .white
         label.backgroundColor = .systemRed
         label.textAlignment = .center
         label.layer.cornerRadius = 8
         label.layer.masksToBounds = true
         return label
     },
     position: .bottomRight,
     animationType: .slideFromBottom,
     stayDuration: 5.0
 )

 // 6. 添加测试色块
 ScreenManagement.shared.addTestColorBlocks()

 // 7. 清空队列
 ScreenManagement.shared.clearQueue()

 // 8. 停止当前播放
 ScreenManagement.shared.stopCurrent()
 */
